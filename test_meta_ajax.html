<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Meta抓取Ajax测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .form-group { margin: 10px 0; }
        label { display: inline-block; width: 100px; }
        input[type="text"] { width: 300px; padding: 5px; }
        input[type="button"] { padding: 8px 15px; margin-left: 10px; }
        .result { margin: 20px 0; padding: 15px; border: 1px solid #ddd; background-color: #f9f9f9; }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        .loading { border-color: #ffc107; background-color: #fff3cd; }
    </style>
</head>
<body>
    <h2>Meta抓取功能测试</h2>
    
    <div class="form-group">
        <label for="web_url">网站域名:</label>
        <input type="text" id="web_url" placeholder="例如: www.baidu.com" value="www.baidu.com">
        <input type="button" id="meta_btn" value="获取Meta信息" onclick="getmeta()">
    </div>
    
    <div class="form-group">
        <label for="web_name">网站名称:</label>
        <input type="text" id="web_name" readonly>
    </div>
    
    <div class="form-group">
        <label for="web_tags">关键词:</label>
        <input type="text" id="web_tags" readonly>
    </div>
    
    <div class="form-group">
        <label for="web_intro">网站描述:</label>
        <input type="text" id="web_intro" readonly style="width: 500px;">
    </div>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <div class="result">
        <h3>测试说明</h3>
        <ul>
            <li>输入域名后点击"获取Meta信息"按钮</li>
            <li>系统会自动抓取网站的标题、关键词和描述</li>
            <li>优化后的功能包含超时控制、错误处理和状态管理</li>
            <li>如果抓取失败，会显示具体的错误信息</li>
        </ul>
        
        <h3>常见测试域名</h3>
        <button onclick="testDomain('www.baidu.com')">百度</button>
        <button onclick="testDomain('github.com')">GitHub</button>
        <button onclick="testDomain('stackoverflow.com')">StackOverflow</button>
        <button onclick="testDomain('www.taobao.com')">淘宝</button>
        <button onclick="testDomain('invalid-domain-test.com')">无效域名测试</button>
    </div>

    <script>
        var sitepath = '.'; // 设置站点路径
        
        // 抓取网站Meta信息（优化版本）
        function getmeta() {
            var url = $("#web_url").val().trim();
            if (url == '') {
                alert('请输入网站域名！');
                $("#web_url").focus();
                return false;
            }
            
            // 防止重复点击
            if ($("#meta_btn").val() == '正在获取，请稍候...') {
                return false;
            }
            
            // 清空之前的结果
            $("#web_name").val('');
            $("#web_tags").val('');
            $("#web_intro").val('');
            $("#result").hide();
            
            var startTime = new Date().getTime();
            
            $("#meta_btn").val('正在获取，请稍候...').prop('disabled', true);
            
            $.ajax({
                type: "GET",
                url: '?mod=ajaxget&type=crawl',
                data: 'url=' + encodeURIComponent(url),
                datatype: "script",
                cache: false,
                timeout: 30000, // 30秒超时
                success: function(data){
                    try {
                        var endTime = new Date().getTime();
                        var duration = endTime - startTime;
                        
                        $("body").append(data);
                        $("#meta_btn").val('重新获取').prop('disabled', false);
                        
                        showResult('success', '获取成功！耗时: ' + duration + 'ms');
                    } catch(e) {
                        console.error('处理返回数据时出错:', e);
                        alert('处理返回数据时出错，请重试');
                        $("#meta_btn").val('重新获取').prop('disabled', false);
                        showResult('error', '处理返回数据时出错: ' + e.message);
                    }
                },
                error: function(xhr, status, error) {
                    var endTime = new Date().getTime();
                    var duration = endTime - startTime;
                    
                    $("#meta_btn").val('重新获取').prop('disabled', false);
                    
                    var errorMsg = '';
                    if (status === 'timeout') {
                        errorMsg = '获取超时，请检查网站是否可以正常访问，或稍后重试';
                    } else if (status === 'error') {
                        if (xhr.status === 0) {
                            errorMsg = '网络连接失败，请检查网络连接';
                        } else {
                            errorMsg = '获取失败 (错误代码: ' + xhr.status + ')，请稍后重试';
                        }
                    } else {
                        errorMsg = '获取失败，请稍后重试';
                    }
                    
                    alert(errorMsg);
                    showResult('error', errorMsg + ' (耗时: ' + duration + 'ms)');
                    console.error('Meta获取失败:', status, error, xhr);
                }
            });
        }
        
        function testDomain(domain) {
            $("#web_url").val(domain);
            getmeta();
        }
        
        function showResult(type, message) {
            $("#result").removeClass('success error loading').addClass(type).html(message).show();
        }
    </script>
</body>
</html>
