<?php
/**
 * Sitemap测试脚本
 * 用于验证sitemap功能是否正常工作
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

// 定义常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

// 包含必要的文件
require(APP_PATH.'init.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');
require(APP_PATH.'module/article.php');
require(APP_PATH.'module/prelink.php');

echo "<h1>Sitemap功能测试</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 测试各种sitemap类型
$sitemap_types = array(
    'index' => 'Sitemap索引',
    'all' => '综合Sitemap',
    'webdir' => '网站目录Sitemap',
    'article' => '文章Sitemap',
    'category' => '分类Sitemap',
    'pages' => '功能页面Sitemap'
);

echo "<h2>可用的Sitemap类型:</h2>\n";
echo "<ul>\n";
foreach ($sitemap_types as $type => $name) {
    $url = "?mod=sitemap&type={$type}&format=xml";
    echo "<li><a href=\"{$url}\" target=\"_blank\">{$name}</a> - <code>{$url}</code></li>\n";
}
echo "</ul>\n";

// 测试URL生成函数
echo "<h2>URL生成函数测试:</h2>\n";
echo "<ul>\n";

// 测试get_module_url
if (function_exists('get_module_url')) {
    echo "<li>get_module_url('webdir'): " . get_module_url('webdir') . "</li>\n";
    echo "<li>get_module_url('article'): " . get_module_url('article') . "</li>\n";
} else {
    echo "<li style='color:red;'>get_module_url函数不存在</li>\n";
}

// 测试get_category_url
if (function_exists('get_category_url')) {
    echo "<li>get_category_url('webdir', 1): " . get_category_url('webdir', 1) . "</li>\n";
} else {
    echo "<li style='color:red;'>get_category_url函数不存在</li>\n";
}

// 测试get_website_url
if (function_exists('get_website_url')) {
    echo "<li>get_website_url(1, true): " . get_website_url(1, true) . "</li>\n";
} else {
    echo "<li style='color:red;'>get_website_url函数不存在</li>\n";
}

// 测试get_article_url
if (function_exists('get_article_url')) {
    echo "<li>get_article_url(1, true): " . get_article_url(1, true) . "</li>\n";
} else {
    echo "<li style='color:red;'>get_article_url函数不存在</li>\n";
}

echo "</ul>\n";

// 测试数据库连接和基本查询
echo "<h2>数据库连接测试:</h2>\n";
try {
    // 测试网站数量
    $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
    echo "<p>已收录网站数量: {$website_count}</p>\n";
    
    // 测试文章数量
    $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
    echo "<p>已发布文章数量: {$article_count}</p>\n";
    
    // 测试分类数量
    $category_count = $DB->get_count($DB->table('categories'), "1=1");
    echo "<p>分类总数: {$category_count}</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>数据库查询错误: " . $e->getMessage() . "</p>\n";
}

// 测试配置信息
echo "<h2>网站配置信息:</h2>\n";
echo "<ul>\n";
echo "<li>网站URL: " . $options['site_url'] . "</li>\n";
echo "<li>网站根路径: " . $options['site_root'] . "</li>\n";
echo "<li>链接结构: " . $options['link_struct'] . "</li>\n";
echo "</ul>\n";

echo "<h2>测试完成</h2>\n";
echo "<p><a href='sitemap.xml'>查看sitemap.xml</a></p>\n";
echo "<p><a href='?mod=sitemap&type=all'>查看综合sitemap</a></p>\n";
?>
