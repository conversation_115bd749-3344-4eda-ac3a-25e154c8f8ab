<?php
/**
 * 优化的Sitemap模块
 * 支持所有可被索引收录的页面类型
 * 包括网站、文章、分类、功能页面等
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$type = trim($_GET['type'] ?? '');
$cate_id = intval($_GET['cid'] ?? 0);
$format = trim($_GET['format'] ?? 'xml');

if (empty($type)) $type = 'all';

// 根据类型生成对应的sitemap
switch ($type) {
    case 'webdir':
    case 'weblink':
        generate_website_sitemap($cate_id, $format);
        break;
    case 'article':
        generate_article_sitemap($cate_id, $format);
        break;
    case 'category':
        generate_category_sitemap($format);
        break;
    case 'pages':
        generate_pages_sitemap($format);
        break;
    case 'index':
        generate_sitemap_index($format);
        break;
    case 'all':
    default:
        generate_comprehensive_sitemap($cate_id, $format);
        break;
}

/**
 * 生成sitemap索引文件
 */
function generate_sitemap_index($format = 'xml') {
    global $options;

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<sitemapindex xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    $base_url = rtrim($options['site_url'], '/');
    $lastmod = date('c');

    // 主要sitemap文件
    $sitemaps = array(
        'all' => array('priority' => 1.0, 'desc' => '综合sitemap'),
        'webdir' => array('priority' => 0.9, 'desc' => '网站目录'),
        'article' => array('priority' => 0.8, 'desc' => '文章内容'),
        'category' => array('priority' => 0.7, 'desc' => '分类页面'),
        'pages' => array('priority' => 0.6, 'desc' => '功能页面')
    );

    foreach ($sitemaps as $type => $info) {
        echo "    <sitemap>\n";
        echo "        <loc>{$base_url}/?mod=sitemap&amp;type={$type}&amp;format=xml</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "    </sitemap>\n";
    }

    echo "</sitemapindex>\n";
}

/**
 * 生成网站sitemap
 */
function generate_website_sitemap($cate_id = 0, $format = 'xml') {
    global $DB, $options;

    $where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";

    // 分类筛选
    if ($cate_id > 0) {
        $cate = get_one_category($cate_id);
        if (!empty($cate)) {
            if ($cate['cate_childcount'] > 0) {
                $where .= " AND cate_id IN (".$cate['cate_arrchildid'].")";
            } else {
                $where .= " AND cate_id=$cate_id";
            }
        }
    }

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    // 获取网站数据
    $sql = "SELECT web_id, web_url, web_ctime, web_utime FROM ".$DB->table('websites');
    $sql .= " WHERE $where ORDER BY web_id DESC LIMIT 1000";

    $query = $DB->query($sql);
    while ($row = $DB->fetch_array($query)) {
        $web_url = str_replace('&', '&amp;', get_website_url($row['web_id'], true));
        $lastmod = date('c', max($row['web_ctime'], $row['web_utime']));

        echo "    <url>\n";
        echo "        <loc>{$web_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>weekly</changefreq>\n";
        echo "        <priority>0.8</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);

    echo "</urlset>\n";
}

/**
 * 生成文章sitemap
 */
function generate_article_sitemap($cate_id = 0, $format = 'xml') {
    global $DB, $options;

    $where = "art_status=3";

    // 分类筛选
    if ($cate_id > 0) {
        $cate = get_one_category($cate_id);
        if (!empty($cate)) {
            if ($cate['cate_childcount'] > 0) {
                $where .= " AND cate_id IN (".$cate['cate_arrchildid'].")";
            } else {
                $where .= " AND cate_id=$cate_id";
            }
        }
    }

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    // 获取文章数据
    $sql = "SELECT art_id, art_ctime, art_utime FROM ".$DB->table('articles');
    $sql .= " WHERE $where ORDER BY art_id DESC LIMIT 500";

    $query = $DB->query($sql);
    while ($row = $DB->fetch_array($query)) {
        $art_url = str_replace('&', '&amp;', get_article_url($row['art_id'], true));
        $lastmod = date('c', max($row['art_ctime'], $row['art_utime']));

        echo "    <url>\n";
        echo "        <loc>{$art_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.7</priority>\n";
        echo "    </url>\n";
    }
    $DB->free_result($query);

    echo "</urlset>\n";
}

/**
 * 生成分类sitemap
 */
function generate_category_sitemap($format = 'xml') {
    global $DB, $options;

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    // 首页
    echo "    <url>\n";
    echo "        <loc>".$options['site_url']."</loc>\n";
    echo "        <lastmod>".date('c')."</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";

    // 获取所有分类
    $categories = get_all_category();
    foreach ($categories as $cate) {
        if ($cate['cate_postcount'] > 0) {
            $cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url($cate['cate_mod'], $cate['cate_id']));

            echo "    <url>\n";
            echo "        <loc>{$cate_url}</loc>\n";
            echo "        <lastmod>".date('c')."</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            if ($cate['cate_mod'] == 'webdir') {
                echo "        <priority>0.8</priority>\n";
            } else {
                echo "        <priority>0.7</priority>\n";
            }
            echo "    </url>\n";

            // 分类分页
            if ($cate['cate_postcount'] > 20) {
                $total_pages = ceil($cate['cate_postcount'] / 20);
                for ($page = 2; $page <= min($total_pages, 10); $page++) {
                    $page_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url($cate['cate_mod'], $cate['cate_id'], $page));
                    echo "    <url>\n";
                    echo "        <loc>{$page_url}</loc>\n";
                    echo "        <lastmod>".date('c')."</lastmod>\n";
                    echo "        <changefreq>weekly</changefreq>\n";
                    echo "        <priority>0.6</priority>\n";
                    echo "    </url>\n";
                }
            }
        }
    }

    echo "</urlset>\n";
}

/**
 * 生成功能页面sitemap
 */
function generate_pages_sitemap($format = 'xml') {
    global $options;

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    $base_url = rtrim($options['site_url'], '/');
    $lastmod = date('c');

    // 主要功能页面
    $main_pages = array(
        'webdir' => array('priority' => '0.9', 'changefreq' => 'daily', 'name' => '网站目录'),
        'article' => array('priority' => '0.8', 'changefreq' => 'daily', 'name' => '文章列表'),
        'weblink' => array('priority' => '0.7', 'changefreq' => 'weekly', 'name' => '网站链接'),
        'category' => array('priority' => '0.7', 'changefreq' => 'weekly', 'name' => '分类浏览'),
        'top' => array('priority' => '0.6', 'changefreq' => 'daily', 'name' => '排行榜'),
        'search' => array('priority' => '0.5', 'changefreq' => 'monthly', 'name' => '搜索页面'),
        'feedback' => array('priority' => '0.4', 'changefreq' => 'monthly', 'name' => '意见反馈'),
        'addurl' => array('priority' => '0.6', 'changefreq' => 'monthly', 'name' => '提交网站'),
        'pending' => array('priority' => '0.3', 'changefreq' => 'daily', 'name' => '待审核'),
        'blacklist' => array('priority' => '0.3', 'changefreq' => 'weekly', 'name' => '黑名单'),
        'vip_list' => array('priority' => '0.5', 'changefreq' => 'weekly', 'name' => 'VIP网站'),
        'datastats' => array('priority' => '0.4', 'changefreq' => 'daily', 'name' => '数据公示')
    );

    foreach ($main_pages as $mod => $info) {
        $page_url = function_exists('get_module_url') ?
                   str_replace('&', '&amp;', $base_url . get_module_url($mod)) :
                   "{$base_url}/?mod={$mod}";

        echo "    <url>\n";
        echo "        <loc>{$page_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>{$info['changefreq']}</changefreq>\n";
        echo "        <priority>{$info['priority']}</priority>\n";
        echo "    </url>\n";
    }

    // 最近更新页面（按天数）
    $update_days = array(1, 3, 7, 15, 30);
    foreach ($update_days as $days) {
        $update_url = function_exists('get_update_url') ?
                     str_replace('&', '&amp;', $base_url . get_update_url($days)) :
                     "{$base_url}/?mod=update&amp;days={$days}";

        echo "    <url>\n";
        echo "        <loc>{$update_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>daily</changefreq>\n";
        echo "        <priority>0.5</priority>\n";
        echo "    </url>\n";
    }

    // 数据归档页面（最近6个月）
    for ($i = 0; $i < 6; $i++) {
        $date = date('Ym', strtotime("-{$i} months"));
        $archives_url = function_exists('get_archives_url') ?
                       str_replace('&', '&amp;', $base_url . get_archives_url($date)) :
                       "{$base_url}/?mod=archives&amp;date={$date}";

        echo "    <url>\n";
        echo "        <loc>{$archives_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>monthly</changefreq>\n";
        echo "        <priority>0.4</priority>\n";
        echo "    </url>\n";
    }

    // RSS订阅页面
    $rss_types = array('webdir', 'article', 'weblink');
    foreach ($rss_types as $rss_type) {
        $rss_url = function_exists('get_rssfeed_url') ?
                  str_replace('&', '&amp;', $base_url . get_rssfeed_url($rss_type, 0)) :
                  "{$base_url}/?mod=rssfeed&amp;type={$rss_type}";

        echo "    <url>\n";
        echo "        <loc>{$rss_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>daily</changefreq>\n";
        echo "        <priority>0.3</priority>\n";
        echo "    </url>\n";
    }

    echo "</urlset>\n";
}

/**
 * 生成综合sitemap（包含所有类型）
 */
function generate_comprehensive_sitemap($cate_id = 0, $format = 'xml') {
    global $DB, $options;

    header("Content-Type: application/xml; charset=utf-8");
    echo "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

    $base_url = rtrim($options['site_url'], '/');
    $lastmod = date('c');

    // 1. 首页
    echo "    <url>\n";
    echo "        <loc>{$base_url}/</loc>\n";
    echo "        <lastmod>{$lastmod}</lastmod>\n";
    echo "        <changefreq>daily</changefreq>\n";
    echo "        <priority>1.0</priority>\n";
    echo "    </url>\n";

    // 2. 主要功能页面
    $main_pages = array(
        'webdir' => array('priority' => '0.9', 'changefreq' => 'daily'),
        'article' => array('priority' => '0.8', 'changefreq' => 'daily'),
        'weblink' => array('priority' => '0.7', 'changefreq' => 'weekly'),
        'category' => array('priority' => '0.7', 'changefreq' => 'weekly'),
        'top' => array('priority' => '0.6', 'changefreq' => 'daily'),
        'addurl' => array('priority' => '0.6', 'changefreq' => 'monthly'),
        'search' => array('priority' => '0.5', 'changefreq' => 'monthly'),
        'vip_list' => array('priority' => '0.5', 'changefreq' => 'weekly'),
        'datastats' => array('priority' => '0.4', 'changefreq' => 'daily'),
        'feedback' => array('priority' => '0.4', 'changefreq' => 'monthly'),
        'pending' => array('priority' => '0.3', 'changefreq' => 'daily'),
        'blacklist' => array('priority' => '0.3', 'changefreq' => 'weekly')
    );

    foreach ($main_pages as $mod => $info) {
        $page_url = function_exists('get_module_url') ?
                   str_replace('&', '&amp;', $base_url . get_module_url($mod)) :
                   "{$base_url}/?mod={$mod}";

        echo "    <url>\n";
        echo "        <loc>{$page_url}</loc>\n";
        echo "        <lastmod>{$lastmod}</lastmod>\n";
        echo "        <changefreq>{$info['changefreq']}</changefreq>\n";
        echo "        <priority>{$info['priority']}</priority>\n";
        echo "    </url>\n";
    }

    // 3. 分类页面（限制数量）
    $categories = get_all_category();
    $category_count = 0;
    foreach ($categories as $cate) {
        if ($cate['cate_postcount'] > 0 && $category_count < 50) {
            $cate_url = str_replace('&', '&amp;', $base_url . get_category_url($cate['cate_mod'], $cate['cate_id']));

            echo "    <url>\n";
            echo "        <loc>{$cate_url}</loc>\n";
            echo "        <lastmod>{$lastmod}</lastmod>\n";
            echo "        <changefreq>weekly</changefreq>\n";
            if ($cate['cate_mod'] == 'webdir') {
                echo "        <priority>0.8</priority>\n";
            } else {
                echo "        <priority>0.7</priority>\n";
            }
            echo "    </url>\n";
            $category_count++;
        }
    }

    echo "</urlset>\n";
}

?>