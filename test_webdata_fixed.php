<?php
// 测试修复后的webdata.php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>测试修复后的webdata.php</h2>";

try {
    // 定义必要的常量
    define('IN_IWEBDIR', true);
    
    // 尝试包含webdata.php文件
    echo "<p>正在包含webdata.php文件...</p>";
    require_once('source/module/webdata.php');
    echo "<p style='color: green;'>✓ webdata.php文件包含成功，没有语法错误</p>";
    
    // 测试关键函数是否存在
    $functions_to_test = array(
        'get_url_content_enhanced',
        'get_sitemeta',
        'extract_meta_from_content', 
        'extract_meta_loose_mode',
        'get_url_content_for_meta_enhanced',
        'get_meta_from_source_view',
        'clean_and_validate_meta',
        'get_simple_url_content'
    );
    
    echo "<h3>检查函数是否正确定义:</h3>";
    $all_functions_exist = true;
    foreach ($functions_to_test as $func) {
        if (function_exists($func)) {
            echo "<p style='color: green;'>✓ 函数 $func 存在</p>";
        } else {
            echo "<p style='color: red;'>✗ 函数 $func 不存在</p>";
            $all_functions_exist = false;
        }
    }
    
    if ($all_functions_exist) {
        echo "<p style='color: green; font-weight: bold;'>✓ 所有关键函数都存在</p>";
        
        // 测试一个简单的Meta提取
        echo "<h3>测试Meta提取功能:</h3>";
        $test_html = '<html><head><title>测试标题</title><meta name="keywords" content="测试,关键词"><meta name="description" content="测试描述"></head></html>';
        
        $meta = extract_meta_from_content($test_html);
        echo "<p><strong>测试结果:</strong></p>";
        echo "<p>标题: " . htmlspecialchars($meta['title']) . "</p>";
        echo "<p>关键词: " . htmlspecialchars($meta['keywords']) . "</p>";
        echo "<p>描述: " . htmlspecialchars($meta['description']) . "</p>";
        
        if (!empty($meta['title']) && !empty($meta['keywords']) && !empty($meta['description'])) {
            echo "<p style='color: green;'>✓ Meta提取功能正常工作</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Meta提取功能可能有问题</p>";
        }
        
        // 测试宽松模式
        echo "<h3>测试宽松模式Meta提取:</h3>";
        $broken_html = '<html><head><title>损坏的HTML测试<meta name="keywords" content="损坏,HTML<meta name="description" content="没有结束引号的描述</head></html>';
        
        $meta_loose = extract_meta_loose_mode($broken_html);
        echo "<p><strong>宽松模式结果:</strong></p>";
        echo "<p>标题: " . htmlspecialchars($meta_loose['title']) . "</p>";
        echo "<p>关键词: " . htmlspecialchars($meta_loose['keywords']) . "</p>";
        echo "<p>描述: " . htmlspecialchars($meta_loose['description']) . "</p>";
        
        if (!empty($meta_loose['title'])) {
            echo "<p style='color: green;'>✓ 宽松模式Meta提取功能正常工作</p>";
        } else {
            echo "<p style='color: orange;'>⚠ 宽松模式Meta提取功能可能有问题</p>";
        }
        
    } else {
        echo "<p style='color: red; font-weight: bold;'>✗ 部分关键函数缺失</p>";
    }
    
} catch (ParseError $e) {
    echo "<p style='color: red;'>✗ 语法错误: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . "</p>";
    echo "<p>行号: " . $e->getLine() . "</p>";
} catch (Error $e) {
    echo "<p style='color: red;'>✗ 致命错误: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . "</p>";
    echo "<p>行号: " . $e->getLine() . "</p>";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 异常: " . $e->getMessage() . "</p>";
    echo "<p>文件: " . $e->getFile() . "</p>";
    echo "<p>行号: " . $e->getLine() . "</p>";
}

echo "<hr>";
echo "<h3>修复总结:</h3>";
echo "<ul>";
echo "<li>✓ 恢复了get_url_content_enhanced函数以保持兼容性</li>";
echo "<li>✓ 保留了所有新增的Meta抓取增强功能</li>";
echo "<li>✓ 修复了函数重复定义的问题</li>";
echo "<li>✓ 删除了重复的函数定义</li>";
echo "<li>✓ 保持了向后兼容性</li>";
echo "</ul>";

echo "<h3>系统信息:</h3>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>内存使用: " . memory_get_usage(true) . " bytes</p>";
?>
