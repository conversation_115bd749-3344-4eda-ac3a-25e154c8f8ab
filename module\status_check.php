<?php
/* --------------------------------------------------
 * 网站状态检测接口（每日只调用一次）
 * -------------------------------------------------- */
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
ob_start('ob_gzhandler');                       // gzip 输出（浏览器自动解压）

/* ---------- 0. 取 url 参数并校验 ---------- */
$url = filter_input(INPUT_GET, 'url', FILTER_SANITIZE_URL);
if (!$url || !filter_var($url, FILTER_VALIDATE_URL)) {
    echo '{"error":"无效 URL"}'; exit;
}

/* ---------- 1. 先查缓存：24 h ---------- */
$cacheKey = 'chk_' . md5($url);
$ttl      = 86400;                              // 24 小时（单位秒）
$cached   = function_exists('apcu_fetch') ? apcu_fetch($cacheKey) : false;

if ($cached !== false) {                        // 命中直接返回
    echo $cached;
    exit;
}

/* ---------- 2. 缓存没命中，才真正去探测 ---------- */
$tsStart = microtime(true);

$ch = curl_init();
curl_setopt_array($ch, [
    CURLOPT_URL            => $url,
    CURLOPT_NOBODY         => true,             // 只取响应头
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_HEADER         => true,
    CURLOPT_FOLLOWLOCATION => false,            // 不跟重定向
    CURLOPT_CONNECTTIMEOUT => 3,
    CURLOPT_TIMEOUT        => 6,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_USERAGENT      => 'StatusCheckBot/1.0'
]);

$head = curl_exec($ch);
$ms   = (int)round(1000 * (microtime(true) - $tsStart));

if ($head === false) {
    $json = json_encode([
        'error'         => '请求超时',
        'response_time' => $ms
    ]);
} else {
    $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $svr  = '';
    if (preg_match('/\r\nServer:\s*([^\r\n]+)/i', $head, $m)) {
        $svr = trim($m[1]);
    }
    $json = json_encode([
        'status'        => $code,
        'server'        => $svr,
        'response_time' => $ms
    ]);
}

curl_close($ch);

/* ---------- 3. 写入缓存并输出 ---------- */
if (function_exists('apcu_store')) {
    apcu_store($cacheKey, $json, $ttl);
} else {
    /* 如果你的主机没有 APCu，可退而求其次：写文件缓存 */
    $dir = __DIR__ . '/cache';              //      /cache 目录需可写
    if (!is_dir($dir))  mkdir($dir,0755,true);
    file_put_contents($dir.'/'.$cacheKey.'.json', $json);
}
echo $json;