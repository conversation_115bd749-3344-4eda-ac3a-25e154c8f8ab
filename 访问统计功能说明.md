# 访问统计功能修改说明

## 概述

本次修改实现了真实的访问统计功能，让数据统计页面显示准确的访问数据，包括：
- **今日总浏览**：站点浏览 + 文章浏览的总数
- **今日站点浏览**：用户点击查看网站详情的次数
- **今日文章浏览**：用户点击查看文章详情的次数  
- **今日出站次数**：用户通过go.php跳转到外部网站的次数

## 修改的文件

### 1. 新增文件

#### `source/module/visit_stats.php`
- 访问统计核心模块
- 提供记录和获取访问统计的函数
- 包含防重复统计机制（基于IP和时间）

#### `data/sql/spider_stats.sql`
- 添加了每日访问统计表的SQL结构
- 表名：`dir_daily_stats`

#### `install_daily_stats.php`
- 数据库表安装脚本
- 可以检查安装状态和查看统计数据

### 2. 修改的文件

#### `module/siteinfo.php`
- 保持原有的 `web_views` 字段更新（兼容性）
- 新增每日统计记录功能
- 防止重复统计（5分钟内同IP不重复计算）

#### `module/artinfo.php`
- 保持原有的 `art_views` 字段更新（兼容性）
- 新增每日统计记录功能
- 防止重复统计（5分钟内同IP不重复计算）

#### `go.php`
- 保持原有的 `web_outstat` 字段更新（兼容性）
- 新增每日统计记录功能
- 支持通过 `id` 参数传递网站ID

#### `module/datastats.php`
- 修改统计数据获取逻辑
- 使用真实的每日访问统计数据
- AJAX更新也使用真实数据

#### `source/module/spider_tracker.php`
- 修改 `get_daily_visit_stats` 函数
- 优先使用真实的每日统计数据

## 数据库表结构

### `dir_daily_stats` 表
```sql
CREATE TABLE `dir_daily_stats` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL COMMENT '统计日期',
  `site_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '站点浏览次数',
  `article_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文章浏览次数',
  `outlink_clicks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '出站点击次数',
  `total_views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '总浏览次数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_date` (`stat_date`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8;
```

## 统计逻辑

### 1. 站点浏览统计
- **触发条件**：用户访问 `?mod=siteinfo&wid=网站ID`
- **统计内容**：每次访问网站详情页面
- **防重复**：同一IP在5分钟内访问同一网站不重复计算
- **兼容性**：同时更新原有的 `dir_webdata.web_views` 字段

### 2. 文章浏览统计
- **触发条件**：用户访问 `?mod=artinfo&aid=文章ID`
- **统计内容**：每次访问文章详情页面
- **防重复**：同一IP在5分钟内访问同一文章不重复计算
- **兼容性**：同时更新原有的 `dir_articles.art_views` 字段

### 3. 出站点击统计
- **触发条件**：用户访问 `go.php?url=目标网址&id=网站ID`
- **统计内容**：每次通过跳转页面访问外部网站
- **防重复**：同一IP在5分钟内点击同一网站不重复计算
- **兼容性**：同时更新原有的 `dir_webdata.web_outstat` 字段

### 4. 总浏览统计
- **计算方式**：站点浏览次数 + 文章浏览次数
- **实时更新**：每次有新的站点或文章浏览时自动更新

## 安装步骤

1. **上传文件**：将所有修改的文件上传到对应位置
2. **运行安装脚本**：访问 `install_daily_stats.php` 创建数据库表
3. **测试功能**：
   - 访问网站详情页面测试站点浏览统计
   - 访问文章详情页面测试文章浏览统计
   - 通过go.php跳转测试出站点击统计
   - 查看数据统计页面确认数据正确显示

## 注意事项

1. **兼容性**：所有修改都保持了与原有系统的兼容性
2. **性能**：使用了session机制防止重复统计，不会影响性能
3. **准确性**：统计数据基于真实的用户行为，不再是模拟数据
4. **安全性**：包含了必要的参数验证和异常处理

## 使用go.php的正确方式

为了正确统计出站点击，需要在生成外部链接时使用以下格式：
```php
$outlink_url = "go.php?url=" . urlencode($website_url) . "&id=" . $website_id;
```

例如：
```html
<a href="go.php?url=https://www.example.com&id=123">访问网站</a>
```

这样可以确保出站点击被正确统计到对应的网站。
