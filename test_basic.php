<?php
// 基本PHP测试
echo "<h2>基本PHP功能测试</h2>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";

// 测试常量定义
define('IN_IWEBDIR', true);
echo "<p>常量定义: ✓</p>";

// 测试基本函数
if (function_exists('curl_init')) {
    echo "<p>CURL扩展: ✓</p>";
} else {
    echo "<p>CURL扩展: ✗</p>";
}

if (function_exists('mb_detect_encoding')) {
    echo "<p>MB扩展: ✓</p>";
} else {
    echo "<p>MB扩展: ✗</p>";
}

// 测试错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);
echo "<p>错误报告已开启</p>";

echo "<p>测试完成，如果看到这条消息说明基本PHP功能正常</p>";
?>
