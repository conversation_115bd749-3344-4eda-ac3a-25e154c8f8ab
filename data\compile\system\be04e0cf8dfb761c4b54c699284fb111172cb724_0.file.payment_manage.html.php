<?php
/* Smarty version 4.5.5, created on 2025-07-16 17:07:17
  from '/www/wwwroot/www.95dir.com/themes/system/payment_manage.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_68776bc59ac966_20199520',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'be04e0cf8dfb761c4b54c699284fb111172cb724' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/payment_manage.html',
      1 => 1752656828,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_68776bc59ac966_20199520 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/www/wwwroot/www.95dir.com/source/extend/smarty/plugins/modifier.truncate.php','function'=>'smarty_modifier_truncate',),));
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="main">
    <div class="title">
        <h2>付费管理</h2>
    </div>
    
    <!-- 统计概览 -->
    <div class="stats-overview" style="margin-bottom: 20px;">
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">付费网站统计</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #ff6600;">
                    <strong>VIP网站：</strong><span style="color: #ff6600; font-size: 18px;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total_vip'];?>
</span>
                </div>
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #28a745;">
                    <strong>推荐网站：</strong><span style="color: #28a745; font-size: 18px;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total_recommend'];?>
</span>
                </div>
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #007bff;">
                    <strong>付费总数：</strong><span style="color: #007bff; font-size: 18px;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total_paid'];?>
</span>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选表单 -->
    <div class="filter-form" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <form method="get" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div>
                    <label>到期状态：</label>
                    <select name="status" style="padding: 5px;">
                        <option value="0">全部</option>
                        <option value="1" <?php if ($_smarty_tpl->tpl_vars['status_filter']->value == 1) {?>selected<?php }?>>正常</option>
                        <option value="2" <?php if ($_smarty_tpl->tpl_vars['status_filter']->value == 2) {?>selected<?php }?>>即将到期</option>
                        <option value="3" <?php if ($_smarty_tpl->tpl_vars['status_filter']->value == 3) {?>selected<?php }?>>已过期</option>
                    </select>
                </div>
                <div>
                    <label>付费类型：</label>
                    <select name="type" style="padding: 5px;">
                        <option value="0">全部</option>
                        <option value="1" <?php if ($_smarty_tpl->tpl_vars['type_filter']->value == 1) {?>selected<?php }?>>VIP</option>
                        <option value="2" <?php if ($_smarty_tpl->tpl_vars['type_filter']->value == 2) {?>selected<?php }?>>推荐</option>
                    </select>
                </div>
                <div>
                    <label>关键词：</label>
                    <input type="text" name="keywords" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" placeholder="网站名称或网址" style="padding: 5px; width: 150px;">
                </div>
                <div>
                    <input type="submit" value="筛选" class="btn" style="padding: 6px 15px;">
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
" class="btn" style="padding: 6px 15px; margin-left: 5px;">重置</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 网站列表 -->
    <div class="list">
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="60">ID</th>
                <th width="120">网站名称</th>
                <th width="150">网站网址</th>
                <th width="100">分类</th>
                <th width="120">付费类型</th>
                <th width="100">到期时间</th>
                <th width="80">状态</th>
                <th width="100">添加时间</th>
                <th width="120">操作</th>
            </tr>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'website');
$_smarty_tpl->tpl_vars['website']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['website']->value) {
$_smarty_tpl->tpl_vars['website']->do_else = false;
?>
            <tr bgcolor="#FFFFFF">
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
</td>
                <td><a href="../?mod=website_detail&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" target="_blank" title="查看网站详情"><?php echo smarty_modifier_truncate($_smarty_tpl->tpl_vars['website']->value['web_name'],15);?>
</a></td>
                <td><a href="<?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>
" target="_blank" title="访问网站"><?php echo smarty_modifier_truncate($_smarty_tpl->tpl_vars['website']->value['web_url'],20);?>
</a></td>
                <td align="center"><?php echo (($tmp = $_smarty_tpl->tpl_vars['website']->value['cate_name'] ?? null)===null||$tmp==='' ? "未分类" ?? null : $tmp);?>
</td>
                <td align="center">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['website']->value['payment_types'], 'type');
$_smarty_tpl->tpl_vars['type']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['type']->value) {
$_smarty_tpl->tpl_vars['type']->do_else = false;
?>
                    <span style="color: <?php echo $_smarty_tpl->tpl_vars['type']->value['color'];?>
; font-weight: bold; display: block;"><?php echo $_smarty_tpl->tpl_vars['type']->value['type'];?>
</span>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </td>
                <td align="center">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['website']->value['payment_types'], 'type');
$_smarty_tpl->tpl_vars['type']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['type']->value) {
$_smarty_tpl->tpl_vars['type']->do_else = false;
?>
                    <span style="display: block; font-size: 12px;"><?php echo $_smarty_tpl->tpl_vars['type']->value['expire_date'];?>
</span>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </td>
                <td align="center"><span style="color: <?php echo $_smarty_tpl->tpl_vars['website']->value['status_color'];?>
; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['website']->value['status_text'];?>
</span></td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['website']->value['web_ctime_formatted'];?>
</td>
                <td align="center">
                    <a href="javascript:void(0)" onclick="renewWebsite(<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
, '<?php echo $_smarty_tpl->tpl_vars['website']->value['web_name'];?>
')" class="btn" style="font-size: 12px; padding: 3px 8px;">续费</a>
                    <a href="../system/website.php?act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" target="_blank" class="btn" style="font-size: 12px; padding: 3px 8px;">编辑</a>
                </td>
            </tr>
            <?php
}
if ($_smarty_tpl->tpl_vars['website']->do_else) {
?>
            <tr bgcolor="#FFFFFF">
                <td colspan="9" align="center" style="padding: 30px; color: #999;">暂无付费网站</td>
            </tr>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php if ($_smarty_tpl->tpl_vars['showpage']->value) {?>
    <div class="pages">
        <?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>

    </div>
    <?php }?>
</div>

<!-- 续费弹窗 -->
<div id="renewModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: #fff; padding: 20px; border-radius: 5px; width: 400px;">
        <h3 style="margin: 0 0 15px 0;">网站续费</h3>
        <form id="renewForm" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
            <input type="hidden" name="act" value="renew">
            <input type="hidden" name="web_id" id="renew_web_id">
            
            <div style="margin-bottom: 15px;">
                <label>网站名称：</label>
                <span id="renew_web_name" style="font-weight: bold;"></span>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>付费类型：</label>
                <select name="payment_type" id="payment_type" style="padding: 5px; width: 100%;" onchange="calculateAmount()">
                    <option value="1">VIP</option>
                    <option value="2">推荐</option>
                </select>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>续费时长：</label>
                <select name="months" id="months" style="padding: 5px; width: 100%;" onchange="calculateAmount()">
                    <option value="1">1个月</option>
                    <option value="3">3个月</option>
                    <option value="6">6个月</option>
                    <option value="12">12个月</option>
                </select>
            </div>
            
            <div style="margin-bottom: 15px;">
                <label>续费金额：</label>
                <span id="amount_display" style="color: #28a745; font-weight: bold; font-size: 16px;">￥0</span>
            </div>
            
            <div style="text-align: right;">
                <button type="button" onclick="closeRenewModal()" style="padding: 8px 15px; margin-right: 10px;">取消</button>
                <button type="submit" style="padding: 8px 15px; background: #007bff; color: #fff; border: none; border-radius: 3px;">确认续费</button>
            </div>
        </form>
    </div>
</div>

<?php echo '<script'; ?>
>
function renewWebsite(webId, webName) {
    document.getElementById('renew_web_id').value = webId;
    document.getElementById('renew_web_name').textContent = webName;
    document.getElementById('renewModal').style.display = 'block';
    calculateAmount();
}

function closeRenewModal() {
    document.getElementById('renewModal').style.display = 'none';
}

function calculateAmount() {
    var paymentType = parseInt(document.getElementById('payment_type').value);
    var months = parseInt(document.getElementById('months').value);
    
    // 通过AJAX获取当前价格
    var xhr = new XMLHttpRequest();
    xhr.open('GET', 'payment_price_api.php?type=' + paymentType + '&months=' + months, true);
    xhr.onreadystatechange = function() {
        if (xhr.readyState === 4 && xhr.status === 200) {
            var response = JSON.parse(xhr.responseText);
            if (response.success) {
                document.getElementById('amount_display').textContent = '￥' + response.amount.toFixed(2);
            } else {
                document.getElementById('amount_display').textContent = '价格获取失败';
            }
        }
    };
    xhr.send();
}

// 点击模态框外部关闭
document.getElementById('renewModal').onclick = function(e) {
    if (e.target === this) {
        closeRenewModal();
    }
}
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
