@charset "utf-8";@import url(reset.css);.bestlist li a,.blank10{display:block}.content img,img{max-width:100%}#topbar-left,.catelist li strong,.count,.logo,.navbar li,.sipt{float:left}#newbox,#quickbox{width:233px}#footer,#options li a,.bestlist li,.bestlist li a,.idclist li,.navbar li{text-align:center}#catebox h2 a,#catebox ul li a,#fmenu a,#options li a,#options li a:hover,.arclist li a,.current,.rellist li a,.sort-show a,.weblist_b li strong a,.wtitle a,a,a:hover{text-decoration:none}#options,.artlist_b,.wdata{list-style:none}.weblist_b li address,.weblist_b li p,.weblist_b li strong{position:absolute;left:90px;overflow:hidden}#cursel,.artlist li p,.artlist_b li,.bestlist li strong,.exlist li,.idclist li strong,.newlist li a,.quicklist li,.rellist li strong,.sitelist li .info p,.toplist li,.weblist_b li p,.weblist_b li strong{overflow:hidden}.artlist_b li,.exlist li,.idclist li strong,.inlist li,.linklist li,.newlist li,.quicklist li,.rellist li strong,.toplist li{white-space:nowrap}.artlist_b li,.params,.sitelist li,.sofrm,.weblist_b li{position:relative}#cursel,#options li,.close,.sbtn{cursor:pointer}*{box-sizing:border-box;-webkit-tap-highlight-color:transparent}body{background:url(bg.png) top repeat-x #fff}body,input,select,td,textarea,th{color:#666;font:12px/1.5 "微软雅黑"}a{color:#666}a:hover{color:#f30}.blank10{height:10px;width:100%}#topbar,#wrapper{margin:0 auto;width:1200px}#mainbox{margin-top:10px}.sitelist li .info p{color:#666;margin-bottom:8px}.sitelist li .info address,.sort-show em{font-style:normal}.bestlist,.inlist1{display:flex;flex-wrap:wrap;margin:0 -10px}.inlist1 li{width:25%;padding:10px}.inlist1 li img{width:100%;height:auto;margin-bottom:5px}.bestlist li{width:20%}.hcatelist{display:flex;flex-wrap:wrap;margin:0 -5px}#cursel,.logo,.sipt,.sofrm{display:block}.exlist,.hcatelist li{padding:5px}.hcatelist li a{padding:5px 10px;background:#eaeef3;border-radius:3px;}#options,.sipt{border:1px solid #dadada}#exlink,#homecate,#idcbox,#inbox,#linkbox,#newbox,#newsbox,#quickbox,.bestlist li img,.idclist li img{border:1px solid #e8e8e8}#topbg{height:32px;line-height:32px}#topbox,.logo{height:100px}#topbar-right,.exlist li span,.newlist li span{color:#ccc;float:right}#topbar-right img{vertical-align:middle}.logo{background:url(logo.png) center;width:220px}#sobox{float:right;padding-top:20px}.sofrm{margin:0 auto;padding-top:14px}.sipt{background:url(ipt.png) top left no-repeat;font:13px/30px normal;height:30px;padding:0 5px 0 90px;width:300px}#footer,.count,.link{padding:0px}.sbtn{background:#65bc0b;border:0;color:#fff;font-size:14px;height:32px;width:70px}#selopt{background:url(select.gif) no-repeat;position:absolute;left:2px;top:17px;width:88px}#cursel{height:28px;line-height:28px;text-indent:12px;width:85px}#options{border-top:0;display:none;position:absolute;left:-2px;width:80px;z-index:1000}#options li{background:#fff;clear:both}#options li a{color:#555;display:block;height:25px;line-height:25px}#options li a:hover,.current{background:#1791de;color:#fff;display:block}.content img{height:auto!important}#login{color:#fff;float:right;padding:8px;text-align:right}#login a{color:#fff}#navbox{background:url(blue.png) repeat-x;display:block;height:35px}.navbar li{font:14px/35px "微软雅黑";height:35px;width:100px}.navbar li a{display:block;color:#fff}.navbar li a:hover{background:#0080c6;display:block;color:#fff}.navbar .navline{background:#0797e5;display:block;height:35px;width:1px}.navbar .cur{background:#0067ae}#txtbox{background:url(blue.png) 0 -55px repeat-x;border-left:1px solid #e2ecf1;border-right:1px solid #e2ecf1;height:40px}#adbox,.ad728x90{height:90px}.count b{color:#f60;font:bold 14px Arial;padding-right:3px}.link{color:#999;float:right}.artlist li h3 a,.link a,.sitelist li .info h3 a{color:#06c}.ad728x90{background:#fff;float:left;width:728px}#mainbox-right,#newbox,#pagebox-right,#quickbox,.artlist_b li span,.fenxiang,.sitestat,.timelink,.toplist li em1{float:right}.ad250x250{height:250px}.sitestat{background:#fff;height:90px;width:210px}.sitestat p{padding:6px 10px}.sitestat span{color:#f60;font:bold 14px Arial}#homebest{border:1px solid #e2ecf1}#bestart h3,#bestweb h3,#exlink h3,#homebest h3,#homecate h3,#idcbox h3,#newbox h3,#newsbox h3,#quickbox h3{background:url(blue.png) 0 -95px repeat-x;font-size:14px;padding:6px}.bestlist{padding:0 8px}.bestlist li{float:left;font-size:12px;padding:15px 13px}.bestlist li img{margin-right:5px;vertical-align:middle;background:#fff;padding:3px}.bestlist li strong{display:block;font-weight:400;height:15px;margin-top:3px;white-space:nowrap;width:128px}#homecate,#idcbox{float:left;width:400px}.catelist,.newlist,.newslist,.quicklist{padding:8px}.catelist li,.idclist{padding:8px 0}.newslist li,.quicklist li{padding:6px 0}.catelist li strong a{background:#09c;color:#fff;font-weight:400;padding:3px;text-align:center}.catelist li p a{margin:0 5px}#exlink,#newsbox{float:left;margin-left:10px;width:290px}.newslist li span,.quicklist li span{color:#ccc;float:right;font-size:10px}#quickbox{background:#fcfcfc}.idclist li{float:left;font-size:12px;padding:5px 12px}.idclist li img{background:#fff;padding:3px}.idclist li strong{display:block;font-weight:400;height:15px;width:108px}.exlist li{padding:6px 0}.newlist li{padding:5px 0}.newlist li a{display:block;width:132px}#inbox,#linkbox{padding:8px}#inbox h3,#linkbox h3{height:20px;float:left;width:60px}.inlist li{float:left;margin:0 25px 8px 0}.linklist li{float:left;margin:0 20px 8px 0}#footer{background:url(fbg.png) repeat-x}#fmenu{color:#ccc;padding-bottom:5px}#fmenu a:hover,#retips a{text-decoration:underline}#fmenu a:hover{color:#f60}#fcopy{line-height:23px}.sitepath{padding:10px 0}#mainbox-left{float:left;width:880px}#mainbox-right{width:310px}#arcbox,#subcate,.aattr1{background:#f7fbfe;border:1px dashed #0089d4;padding:10px}#artinfo,#linkinfo,#listbox,#relsite,#siteinfo,.rellist li img{border:1px solid #e8e8e8}#listbox h2,#relsite h2,#subbox h2,#subcate h3{color:#333;font-size:14px}.scatelist{padding-top:5px}.scatelist li{display:block;float:left;height:20px;line-height:20px;width:95px}.scatelist li a{color:#555;font-size:13px;}.scatelist li em{color:#ccc;font:10px normal}.scatelist .highlight a{color:#f60;font-weight:700}.sort-show{background:#fcfcfc;border-bottom:1px solid #f3f3f3;padding:6px}#otherinfo h2,.gray-bg{background:#f9f9f9}.rellist li a,.sort-show a{color:#05c}#listbox,#relsite{background:#fff;padding:10px}.timelink a{color:#06c;font-size:12px;font-weight:400;margin:0 5px}a.timelink_bg{background:#08c;color:#fff;padding:3px}.addfav,.cate a{color:#77c}.artlist li,.sitelist li{border-bottom:1px dashed #e8e8e8;padding:15px 5px;display:block}#arcbox h3{color:#333;font-size:14px;padding-bottom:10px}.arclist li h3{color:#05c}.arclist li p{line-height:23px;padding:3px 0}.arclist li a{color:#555;padding:0 7px}#catebox,.artlist,.sitelist{margin:5px 0}.sitelist li{min-height:120px;height:85px}.sitelist li .thumb{margin-right:15px;width:110px;height:90px;background:#fff;border:1px solid #d7dde3;float:left;padding:3px;position:absolute}.sitelist li .info{margin-left:115px;position:absolute;width:540px}.sitelist li .info h3{margin-bottom:8px;display:block;font-weight:400;font-size:14px;height:15px;line-height:13px;position:absolute;top:0}.sitelist li .info p{display:block;font:12px/25px '宋体';height:45px;position:absolute;top:20px;width:750px}.sitelist li .info address{color:#080;font-size:12px;padding:0 10px 3px 0;position:absolute;top:73px}.addfav{height:18px;width:18px}.gre,.toplist li em a,.visit{color:#080}.visit{background:url(visit.gif) right no-repeat;padding-right:15px}.org,.wtitle a{color:#fff}.artlist li h3{display:block;font:14px normal}.artlist li p{display:block;font:12px/23px '宋体';height:20px;padding-top:3px}.artlist li cite{color:#080;font-size:12px;padding:0 10px 3px 0}.weblink{margin:10px 0}.weblink th{background:#f5fbfe;height:25px}.weblink td{border-bottom:1px dashed #ccc;height:35px}.showpage{display:block;font-size:12px;text-align:left;padding:10px 0}.atitle,.wtitle{font:24px "微软雅黑"}.aattr,.atitle,.pagenav li,.rellist li,.wdata li{text-align:center}.first_page,.jump_page,.last_page,.next_page,.pages,.prev_page,.total_page{border:1px solid #096cb2;padding:2px 6px;margin-right:5px;display:block;float:left}.jump_page,.total_page{background:#0080c6;color:#fff}.first_page,.last_page,.next_page,.pages,.prev_page{background:#fff;color:#486ba2;text-decoration:none}.current{background:#0080c6;color:#fff;display:block;float:left;margin-right:5px;padding:3px 6px}#artinfo,#siteinfo{border-top:2px solid #08c;padding:8px}.wtitle{padding-bottom:15px}.wtitle font{font:12px normal}.wdata{background:#fcfcfc;border-top:1px dotted #dadada;border-bottom:1px dotted #dadada;height:50px;padding:5px}.wdata li{color:#9177aa;display:block;float:left;padding:0 5px}.wdata li em{color:#666;display:block;font-size:12px;padding-top:3px}.line{border-right:1px solid #ddd}#retips{display:block;height:13px;margin-top:10px}#retips a{font-size:12px}.params{padding-top:15px}.wthumb{background:#fff;border:1px solid #dadada;float:left;padding:1px;width:140px}.linkname,.pagenav li{border-bottom:1px dashed #e8e8e8}.linkitem,.siteitem{margin-left:160px}.siteitem li{padding-bottom:8px}.rellist li{float:left;font-size:12px;margin:1px;padding:10px 0 5px 13px}.rellist li img{background:#fff;padding:2px}.rellist li strong{display:block;font-weight:400;height:15px;width:106px}#catebox h2 a:hover,#catebox ul li a:hover,.rellist li a:hover{color:#f30;text-decoration:underline}.atitle{color:#06c;padding:8px 0}.aattr{background:#fcfcfc;padding:3px}.content{font:14px/25px normal;padding:10px 0:font-family:"Apple Color Emoji","Segoe UI Emoji","Noto Color Emoji",sans-serif}#linkinfo{border-top:2px solid #08c;padding:10px}.linkname{color:#f60;font:24px "微软雅黑";padding-bottom:15px}#allcate,#bestart,#bestweb,#subbox,.topsite{border:1px solid #e8e8e8}.linkitem li{padding-bottom:10px}#otherinfo h2{color:#333;font-size:14px;margin-bottom:10px}#otherinfo p{line-height:25px;padding:0 10px}#allcate,#subbox{padding:10px}#catebox h2{color:#333}#catebox h2 a,.weblist_b li strong a{color:#06c}#catebox h2 em,#catebox ul li em{color:#888;font:10px normal}#catebox ul{display:block;margin-top:2px}#catebox ul li{float:left;height:23px;line-height:23px;margin:1px;text-indent:30px;width:121px}#catebox ul li a{color:#444}.artlist_b{padding:0 10px 0 0;counter-reset:rank}.artlist_b li{padding:6px 0 6px 26px;text-overflow:ellipsis;counter-increment:rank}.artlist_b li::before{content:counter(rank);position:absolute;left:5px;top:50%;transform:translateY(-50%);font:700 18px/1 'Roboto Condensed',Arial,sans-serif;color:#666}.artlist_b li:first-child::before{color:#ff4757;font-size:22px}.artlist_b li:nth-child(2)::before{color:#ffa502;font-size:20px}.artlist_b li:nth-child(3)::before{color:#2ed573;font-size:18px}.artlist_b li:nth-child(n+4)::before{color:#a4b0be;font-weight:500}.weblist_b{padding:0 10px}.weblist_b li{padding:8px 0 5px}.weblist_b li img{background:#fff;border:1px solid #dbdbdb;height:65px;width:85px}.weblist_b li strong{display:block;font:12px '';height:15px;top:8px;width:135px}.weblist_b li p{display:block;height:35px;line-height:18px;top:25px}.weblist_b li address{display:block;height:15px;top:60px;width:180px}.topsite{float:left;padding:1px;width:390px;margin:5px;}.topsite h3{background:url(blue.png) 0 -95px repeat-x;font-size:13px;padding:6px}.fbform li,.toplist{padding:10px}.toplist li{padding:3px 0}.toplist li span{color:#f30;padding-right:10px}.toplist li em{font:12px Arial}.fbform{margin-left:200px}.fbform li strong{float:left;font-weight:400;text-align:right;width:60px}.fbform li p{float:left;margin-left:5px}.fbform li span{color:#999}.fipt{background:url(ipt.png) left top no-repeat;border:1px solid #dadada;padding:4px}.fbtn{background:#08c;border:1px solid #08c;color:#fff;font-weight:700;padding:3px 8px}#pagebox-left{float:left;width:310px}#pagebox-right{width:880px}.pagenav{background:#f9f9f9;border:1px solid #e8e8e8}.pagenav li{margin:5px;padding:7px}.prevnext li{width:100%;margin-top:20px}.donate-button-container button{padding:10px 20px;background-color:#4caf50;color:#fff;border:none;border-radius:5px;cursor:pointer;font-size:14px;}.donate-button-container button:hover{background-color:#45a049}#donate-popup{position:fixed;width:100%;height:100%;top:0;left:0;background-color:rgba(0,0,0,.8);display:flex;justify-content:center;align-items:center;z-index:9999}.donate-popup-content{background:#fff;padding:20px;border-radius:10px;position:relative;width:300px}.close{position:absolute;top:10px;right:15px;font-size:24px}.donate-qr-codes div{text-align:center;margin-top:10px}.donate-qr-codes img{width:150px;height:150px}.web_ai_intro{font-size:14px;display:inline-table;width:auto;padding:0 2em 0 .5em;color:#fff;border-radius:1em 4em 0 1em;line-height:1.6em;background:linear-gradient(to right,#ff5e00,#ffb200); font-family:"Apple Color Emoji","Segoe UI Emoji","Noto Color Emoji",sans-serif}.access-button {display: inline-block;  /* 确保按钮作为内联块元素显示 */padding: 10px 20px;     /* 内边距，使按钮有足够空间 */background-color: #007bff;  /* 蓝色背景色（可根据主题更换，如 #4CAF50） */color: white;           /* 文字颜色 */text-decoration: none;  /* 去除默认的下划线 */border-radius: 5px;     /* 圆角边框，增加现代感 */border: none;           /* 无边框 */font-size: 14px;        /* 字体大小 */font-weight: bold;      /* 字体加粗 */cursor: pointer;        /* 鼠标指针变为手型 */transition: all 0.3s ease;  /* 平滑过渡效果 */box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);  /* 轻微初始阴影 */}.access-button:hover {background-color: #0056b3;  /* 悬停时背景色变深 */box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);  /* 增加阴影，实现凸起效果 */transform: translateY(-2px);  /* 向上移动 2px，模拟凸起 */}@media (min-width:768px){.wap{display:none}}@media (max-width:767px){#topbox,.logo{height:60px}.logo,.navbar li,.sipt,.wdata li,.wthumb{float:left}#catebox ul li,.navbar li,.rellist li,.wdata li{text-align:center}body{background:#fff}#wrapper{margin:0 auto;width:100%}#header{margin-top:-20px}.clearfix{clear:both}#navbox{background:#0089d4;display:block;height:106px}.navbar li{font:14px/35px "微软雅黑";height:35px;width:25%}.navbar li:nth-last-child(2),li.navline{display:none!important}.logo,.sipt,.sofrm,.wdata li{display:block}.logo{background:url(logo.png) center/100% 100%;width:140px}#sobox{float:right;padding-top:0}.sipt{background:url(ipt.png) top left no-repeat;border:1px solid #dadada;font:13px/30px normal;height:30px;padding:0 5px 0 90px;width:176px}.sofrm{margin:0 auto;padding-top:14px;position:relative;width:auto}.sbtn{background:#65bc0b;border:0;color:#fff;cursor:pointer;font-size:14px;height:32px;width:50px;margin-right:3px}#mainbox-right,#pagebox-left,#pagebox-right,.wthumb{width:100%}#mainbox #mainbox-left{float:left;width:100%}.wdata{background:#fcfcfc;border-top:1px dotted #dadada;border-bottom:1px dotted #dadada;height:80px;list-style:none;padding:5px}.wdata li{color:#9177aa;width:20%;padding:0}.line{border-right:0px solid #ddd}.wthumb{background:#fff;border:0 solid #dadada;padding:0 0 10px;margin:0;height:auto}.fbform,.linkitem,.siteitem{margin-left:0}.topsite{border:1px solid #e8e8e8;float:none;padding:0;width:99%;margin:0 auto 10px;margin-top:0!important;margin-right:0!important}.sitelist li .thumb{width:25%;height:75px}.sitelist li .info{margin-left:30%;width:69%}.sitelist li .info p{height:40px!important;line-height:20px;width:auto}.sitelist li .info address{color:#080;font-size:12px;padding:0 10px 3px 0;position:absolute;top:63px}.sitelist li .info h3{font-size:14px;height:15px;line-height:17px;overflow:hidden}.linkname{font:18px "微软雅黑"}.atitle{font:20px "微软雅黑"}.sitepath{margin:0 1%}#inbox,#mainbox{margin:1%}#catebox ul li{float:left;text-indent:0;width:24%}#listbox h2{color:#333;font-size:14px;margin-bottom:20px}.content img{max-width:100%!important;height:auto!important}#topbar{display:none}.rellist li{float:left;font-size:12px;margin:1px;padding:10px 0 0 15px}#topbg{height:20px;line-height:32px}.count{float:left;padding:6px}}@media(max-width:480px){.rellist li {float: left;font-size: 12px;margin: 1px;padding: 10px 0px 0px 10px;}}/* NEW图标样式 */
/* NEW图标样式 - 强制优先级，确保显示 */
span.new-icon, .new-icon, a .new-icon {
    display: inline-block !important;
    background: #ff4757 !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    font-family: Arial, sans-serif !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    margin-left: 6px !important;
    margin-right: 2px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4) !important;
    border: none !important;
    text-decoration: none !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    position: relative !important;
    animation: newIconBlink 2s ease-in-out infinite !important;
    min-width: 20px !important;
    text-align: center !important;
}

/* 闪烁动画效果 */
@keyframes newIconBlink {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 悬停效果 */
.new-icon:hover {
    background: #ff3742 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 3px 6px rgba(255, 71, 87, 0.6) !important;
}

/* 响应式调整 */
@media (max-width: 767px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 8px !important;
        padding: 1px 4px !important;
        margin-left: 4px !important;
        border-radius: 3px !important;
    }
}

@media (max-width: 480px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 7px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* NEW图标样式 - 强制优先级，确保显示 */
span.new-icon, .new-icon, a .new-icon {
    display: inline-block !important;
    background: #ff4757 !important;
    color: #ffffff !important;
    font-size: 10px !important;
    font-weight: bold !important;
    font-family: Arial, sans-serif !important;
    padding: 2px 6px !important;
    border-radius: 4px !important;
    margin-left: 6px !important;
    margin-right: 2px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 2px 4px rgba(255, 71, 87, 0.4) !important;
    border: none !important;
    text-decoration: none !important;
    line-height: 1.2 !important;
    white-space: nowrap !important;
    z-index: 1000 !important;
    position: relative !important;
    animation: newIconBlink 2s ease-in-out infinite !important;
    min-width: 20px !important;
    text-align: center !important;
}

/* 闪烁动画效果 */
@keyframes newIconBlink {
    0% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.7; transform: scale(1.1); }
    100% { opacity: 1; transform: scale(1); }
}

/* 悬停效果 */
.new-icon:hover {
    background: #ff3742 !important;
    transform: scale(1.05) !important;
    box-shadow: 0 3px 6px rgba(255, 71, 87, 0.6) !important;
}

/* 响应式调整 */
@media (max-width: 767px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 8px !important;
        padding: 1px 4px !important;
        margin-left: 4px !important;
        border-radius: 3px !important;
    }
}

@media (max-width: 480px) {
    span.new-icon, .new-icon, a .new-icon {
        font-size: 7px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* NEW图标样式 - 使用!important确保优先级 */
.new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    box-shadow: 0 1px 3px rgba(255, 71, 87, 0.3) !important;
    animation: newPulse 2s infinite !important;
    position: relative !important;
    z-index: 999 !important;
    white-space: nowrap !important;
    line-height: 1 !important;
    border: none !important;
    text-decoration: none !important;
    min-width: auto !important;
    width: auto !important;
    height: auto !important;
}

/* 确保NEW图标在链接中正常显示 */
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
}

/* 悬停效果 */
.new-icon:hover {
    background: linear-gradient(45deg, #ff3742, #ff5865) !important;
    transform: scale(1.1) !important;
}

@keyframes newPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
}

/* 响应式调整 */
@media (max-width: 767px) {
    .new-icon {
        font-size: 9px !important;
        padding: 1px 3px !important;
        margin-left: 3px !important;
    }
}

/* 确保在不同容器中都能正常显示 */
.newlist .new-icon,
.quicklist .new-icon,
.newslist .new-icon,
li .new-icon,
a .new-icon {
    display: inline-block !important;
    background: linear-gradient(45deg, #ff4757, #ff6b7a) !important;
    color: white !important;
    font-size: 10px !important;
    font-weight: bold !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    margin-left: 5px !important;
    vertical-align: middle !important;
    text-transform: uppercase !important;
    animation: newPulse 2s infinite !important;
}