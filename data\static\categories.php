<?php
//File name: categories.php
//Creation time: 2025-07-21 22:41:50

if (!defined('IN_HANFOX')) exit('Access Denied');

$static_data = array(
	'1' => array(
		'cate_id' => '1',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '人工智能',
		'cate_dir' => 'ai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '人工智能网站',
		'cate_description' => '收录人工智能网站',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '1,2,12,13,17,18,45',
		'cate_childcount' => '6',
		'cate_postcount' => '95'
	),
	'2' => array(
		'cate_id' => '2',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI模型',
		'cate_dir' => 'aimoxing',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'ai模型',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '2',
		'cate_childcount' => '0',
		'cate_postcount' => '16'
	),
	'3' => array(
		'cate_id' => '3',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '电脑网络',
		'cate_dir' => 'dnwl',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电脑网络网站分类目录',
		'cate_description' => '电脑网络网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '3,9,11,24,28,29,30,31,33,35,36,47,50,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,316',
		'cate_childcount' => '44',
		'cate_postcount' => '194'
	),
	'4' => array(
		'cate_id' => '4',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '生活服务',
		'cate_dir' => 'shfw',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '生活服务网站分类目录',
		'cate_description' => '生活服务网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '4,14,15,39,43,49,264,265,266,267,268,269,258,259,260,261,254,243,244,245,246,247,248,249,262,263,257,256,255,253,252,251,250,242,241,275,276,277,278,279,271,272,273,274,270,280,281,282,283,284',
		'cate_childcount' => '49',
		'cate_postcount' => '62'
	),
	'5' => array(
		'cate_id' => '5',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '休闲娱乐',
		'cate_dir' => 'xxyl',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '休闲娱乐网站分类目录',
		'cate_description' => '休闲娱乐网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '5,16,32,38,293,292,291,290,289,288,287,286,285,294,295,296,297,298,299,300,301,302,303,304',
		'cate_childcount' => '23',
		'cate_postcount' => '45'
	),
	'6' => array(
		'cate_id' => '6',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '教育文化',
		'cate_dir' => 'jywh',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '教育文化网站分类目录',
		'cate_description' => '教育文化网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '6,21,40,48,232,233,234,235,236,237,238,231,225,226,227,228,229,240,239,230',
		'cate_childcount' => '19',
		'cate_postcount' => '11'
	),
	'7' => array(
		'cate_id' => '7',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '行业企业',
		'cate_dir' => 'hyqy',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '行业企业网站分类目录',
		'cate_description' => '行业企业网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '7,20,41,314,311,312,313,310,309,307,306,315,308,305',
		'cate_childcount' => '13',
		'cate_postcount' => '12'
	),
	'8' => array(
		'cate_id' => '8',
		'root_id' => '0',
		'cate_mod' => 'webdir',
		'cate_name' => '综合其他',
		'cate_dir' => 'zhqt',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '综合其他网站分类目录',
		'cate_description' => '综合其他网站分类目录',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '8,22,25,42,46,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193',
		'cate_childcount' => '24',
		'cate_postcount' => '21'
	),
	'9' => array(
		'cate_id' => '9',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '网站目录',
		'cate_dir' => 'wzml',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '网站目录分类目录',
		'cate_description' => '网站目录分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '9',
		'cate_childcount' => '0',
		'cate_postcount' => '53'
	),
	'10' => array(
		'cate_id' => '10',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '版本更新',
		'cate_dir' => 'bbgx',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '95分类目录程序更新',
		'cate_description' => '记录95分类目录程序更新',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '10',
		'cate_childcount' => '0',
		'cate_postcount' => '6'
	),
	'11' => array(
		'cate_id' => '11',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '搜索引擎',
		'cate_dir' => 'ssyq',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '搜索引擎网站',
		'cate_description' => '收录搜索引擎网站',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '11',
		'cate_childcount' => '0',
		'cate_postcount' => '13'
	),
	'12' => array(
		'cate_id' => '12',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI编程',
		'cate_dir' => 'aibc',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'AI编程分类目录',
		'cate_description' => 'AI编程分类目录',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '12',
		'cate_childcount' => '0',
		'cate_postcount' => '2'
	),
	'13' => array(
		'cate_id' => '13',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI音视频',
		'cate_dir' => 'aiysp',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'AI音视频分类目录',
		'cate_description' => 'AI音视频分类目录',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '13',
		'cate_childcount' => '0',
		'cate_postcount' => '2'
	),
	'14' => array(
		'cate_id' => '14',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '网购',
		'cate_dir' => 'wg',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '网购分类目录',
		'cate_description' => '网购分类目录',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '14',
		'cate_childcount' => '0',
		'cate_postcount' => '8'
	),
	'15' => array(
		'cate_id' => '15',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '查询',
		'cate_dir' => 'cx',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '查询分类目录',
		'cate_description' => '查询分类目录',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '15',
		'cate_childcount' => '0',
		'cate_postcount' => '3'
	),
	'16' => array(
		'cate_id' => '16',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '影音',
		'cate_dir' => 'yy',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '影音分类目录',
		'cate_description' => '影音分类目录',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '16',
		'cate_childcount' => '0',
		'cate_postcount' => '13'
	),
	'17' => array(
		'cate_id' => '17',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI问答',
		'cate_dir' => 'aiwd',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'AI问答分类目录',
		'cate_description' => 'AI问答分类目录',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '17',
		'cate_childcount' => '0',
		'cate_postcount' => '41'
	),
	'18' => array(
		'cate_id' => '18',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI工具',
		'cate_dir' => 'aigj',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'AI工具分类目录',
		'cate_description' => 'AI工具分类目录',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '18',
		'cate_childcount' => '0',
		'cate_postcount' => '26'
	),
	'19' => array(
		'cate_id' => '19',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '站长教程',
		'cate_dir' => 'zzjc',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '站长教程',
		'cate_description' => '分享站长教程',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '19',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'20' => array(
		'cate_id' => '20',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '企业',
		'cate_dir' => 'gsqy',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '企业网站目录',
		'cate_description' => '企业网站目录',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '20',
		'cate_childcount' => '0',
		'cate_postcount' => '11'
	),
	'21' => array(
		'cate_id' => '21',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '教育',
		'cate_dir' => 'jyjg',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '教育网站目录',
		'cate_description' => '教育网站目录',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '21',
		'cate_childcount' => '0',
		'cate_postcount' => '2'
	),
	'22' => array(
		'cate_id' => '22',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '行政机构',
		'cate_dir' => 'xzjg',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '行政机构网站目录',
		'cate_description' => '行政机构网站目录',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '22',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'23' => array(
		'cate_id' => '23',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '35dir专区',
		'cate_dir' => 'dir35',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '35dir',
		'cate_description' => '35dir相关代码问题',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '23',
		'cate_childcount' => '0',
		'cate_postcount' => '25'
	),
	'24' => array(
		'cate_id' => '24',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '资源',
		'cate_dir' => 'zy',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '资源分类目录',
		'cate_description' => '资源网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '24',
		'cate_childcount' => '0',
		'cate_postcount' => '22'
	),
	'25' => array(
		'cate_id' => '25',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '论坛社区',
		'cate_dir' => 'ltsq',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '论坛社区分类目录',
		'cate_description' => '论坛社区网站分类目录',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '25',
		'cate_childcount' => '0',
		'cate_postcount' => '9'
	),
	'26' => array(
		'cate_id' => '26',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '站长推广',
		'cate_dir' => 'zztg',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '站长推广',
		'cate_description' => '推广软文，推词',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '26',
		'cate_childcount' => '0',
		'cate_postcount' => '63'
	),
	'28' => array(
		'cate_id' => '28',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '建站',
		'cate_dir' => 'jz',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '建站网站分类目录',
		'cate_description' => '建站网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '28',
		'cate_childcount' => '0',
		'cate_postcount' => '12'
	),
	'29' => array(
		'cate_id' => '29',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '站长',
		'cate_dir' => 'zz',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '站长类网站分类目录',
		'cate_description' => '站长类网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '29',
		'cate_childcount' => '0',
		'cate_postcount' => '3'
	),
	'30' => array(
		'cate_id' => '30',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '在线工具',
		'cate_dir' => 'zxgj',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '在线工具网站分类目录',
		'cate_description' => '在线工具网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '30',
		'cate_childcount' => '0',
		'cate_postcount' => '52'
	),
	'31' => array(
		'cate_id' => '31',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '开源项目',
		'cate_dir' => 'kyxm',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '开源项目网站分类目录',
		'cate_description' => '开源项目网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '31',
		'cate_childcount' => '0',
		'cate_postcount' => '10'
	),
	'32' => array(
		'cate_id' => '32',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '游戏',
		'cate_dir' => 'yx',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '游戏网站分类目录',
		'cate_description' => '游戏网站分类目录',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '32',
		'cate_childcount' => '0',
		'cate_postcount' => '12'
	),
	'33' => array(
		'cate_id' => '33',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '公益',
		'cate_dir' => 'gy',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '公益网站分类目录',
		'cate_description' => '公益网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '33',
		'cate_childcount' => '0',
		'cate_postcount' => '8'
	),
	'34' => array(
		'cate_id' => '34',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '热点资讯',
		'cate_dir' => 'rdzx',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '热点资讯',
		'cate_description' => '',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '34',
		'cate_childcount' => '0',
		'cate_postcount' => '2'
	),
	'35' => array(
		'cate_id' => '35',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '博客',
		'cate_dir' => 'bk',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '博客分类目录网站',
		'cate_description' => '博客分类目录网站',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '35',
		'cate_childcount' => '0',
		'cate_postcount' => '13'
	),
	'36' => array(
		'cate_id' => '36',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '邮箱',
		'cate_dir' => 'youxiang',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '邮箱网站分类目录',
		'cate_description' => '邮箱网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '36',
		'cate_childcount' => '0',
		'cate_postcount' => '8'
	),
	'37' => array(
		'cate_id' => '37',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '资源专区',
		'cate_dir' => 'zyzq',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '资源分享',
		'cate_description' => '',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '37',
		'cate_childcount' => '0',
		'cate_postcount' => '7'
	),
	'38' => array(
		'cate_id' => '38',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '动漫',
		'cate_dir' => 'dm',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '动漫网站分类目录',
		'cate_description' => '动漫网站分类目录',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '38',
		'cate_childcount' => '0',
		'cate_postcount' => '14'
	),
	'39' => array(
		'cate_id' => '39',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '新闻',
		'cate_dir' => 'xwmt',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '新闻网站分类目录',
		'cate_description' => '新闻网站分类目录',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '39',
		'cate_childcount' => '0',
		'cate_postcount' => '5'
	),
	'40' => array(
		'cate_id' => '40',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '培训',
		'cate_dir' => 'px',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '培训网站分类目录',
		'cate_description' => '培训网站分类目录',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '40',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'41' => array(
		'cate_id' => '41',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '家政',
		'cate_dir' => 'jz',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '家政网站分类目录',
		'cate_description' => '家政网站分类目录',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '41',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'42' => array(
		'cate_id' => '42',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '名站酷站',
		'cate_dir' => 'mzkz',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '名站酷站类网站分类目录',
		'cate_description' => '名站酷站类网站分类目录',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '42',
		'cate_childcount' => '0',
		'cate_postcount' => '11'
	),
	'43' => array(
		'cate_id' => '43',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '百科',
		'cate_dir' => 'bk',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '百科类网站分类目录',
		'cate_description' => '百科类网站分类目录',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '43',
		'cate_childcount' => '0',
		'cate_postcount' => '45'
	),
	'44' => array(
		'cate_id' => '44',
		'root_id' => '0',
		'cate_mod' => 'article',
		'cate_name' => '百科精选',
		'cate_dir' => 'bkjx',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '生活百科,生活常识',
		'cate_description' => '精选生活百科,生活常识',
		'cate_arrparentid' => '0',
		'cate_arrchildid' => '44',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'45' => array(
		'cate_id' => '45',
		'root_id' => '1',
		'cate_mod' => 'webdir',
		'cate_name' => 'AI导航',
		'cate_dir' => 'aidir',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,1',
		'cate_arrchildid' => '45',
		'cate_childcount' => '0',
		'cate_postcount' => '4'
	),
	'46' => array(
		'cate_id' => '46',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '分类找不到点这里',
		'cate_dir' => 'fenlei',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '46',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'47' => array(
		'cate_id' => '47',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '硬件',
		'cate_dir' => 'yingjian',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '硬件相关分类目录站点',
		'cate_description' => '收录硬件相关分类目录站点',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '47',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'48' => array(
		'cate_id' => '48',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '学校',
		'cate_dir' => 'xuexiao',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '学校分类目录',
		'cate_description' => '学校分类目录网站',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '48',
		'cate_childcount' => '0',
		'cate_postcount' => '8'
	),
	'49' => array(
		'cate_id' => '49',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '相亲',
		'cate_dir' => 'xiangqin',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '相亲类网站目录',
		'cate_description' => '相亲类网站目录',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '49',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'50' => array(
		'cate_id' => '50',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '软件',
		'cate_dir' => 'ruanjian',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '软件分类目录',
		'cate_description' => '软件分类目录网站',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '50',
		'cate_childcount' => '0',
		'cate_postcount' => '3'
	),
	'124' => array(
		'cate_id' => '124',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '资讯',
		'cate_dir' => 'ITzixun',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'IT，IT资讯网址大全',
		'cate_description' => 'IT网址大全是为您精心挑选出国内外最优秀的IT网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；IT网址大全下设分类：IT资讯，IT博客，网络编辑。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '124',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'125' => array(
		'cate_id' => '125',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '数码',
		'cate_dir' => 'shuma',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '数码，数码网址大全',
		'cate_description' => '数码网址大全是为您精心挑选出国内外最优秀的数码网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；数码网址大全下设分类：数码综合，数码论坛，数码影像，数码相关。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '125',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'126' => array(
		'cate_id' => '126',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '下载',
		'cate_dir' => 'xiazai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '126',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'127' => array(
		'cate_id' => '127',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '编程',
		'cate_dir' => 'biancheng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '编程，编程网址大全',
		'cate_description' => '编程网址大全是为您精心挑选出国内外最优秀的编程网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；编程网址大全下设分类：编程开发，程序设计，CCC，Visual Basic，Delphi，Power Builder，Java，LinuxUnix，ASP，PHP，源码下载。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '127',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'128' => array(
		'cate_id' => '128',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '搜索',
		'cate_dir' => 'sousuo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搜索引擎,搜索引擎排名',
		'cate_description' => '搜索网址大全是为您精心挑选出国内外最优秀的建站网站，并保持定期更新和检查，确保您以最安全、最方便的方式，找到您的需要；搜索网址大全下设分类：搜索引擎，搜索引擎排名，全国搜索引擎网站,。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '128',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'129' => array(
		'cate_id' => '129',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '网址',
		'cate_dir' => 'url',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '129',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'130' => array(
		'cate_id' => '130',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '联盟',
		'cate_dir' => 'lianmeng',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '130',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'131' => array(
		'cate_id' => '131',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '微博',
		'cate_dir' => 'weibo',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '131',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'132' => array(
		'cate_id' => '132',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '域名',
		'cate_dir' => 'yuming',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '132',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'133' => array(
		'cate_id' => '133',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => 'DNS',
		'cate_dir' => 'dns',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '133',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'134' => array(
		'cate_id' => '134',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '设计',
		'cate_dir' => 'sheji',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '设计网站大全,国外设计网站大全,国外的设计网站大全,室内设计网站大全',
		'cate_description' => '设计网站大全:最专业权威的设计网址导航。及时收录包括网页设计、平面设计、室内设计、包装设计、建筑设计、工业设计、服装设计、设计竞标、品牌设计公司、设计工作室等...',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '134',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'135' => array(
		'cate_id' => '135',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '素材',
		'cate_dir' => 'sucai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '素材网站大全,国外素材网站大全,设计素材网站大全,免费素材网站大全',
		'cate_description' => '素材网站大全_免费素材共享平台.图片素材图库提供海量素材,图片下载,设计素材,PSD源文件,矢量图,AI,CDR,EPS等高清图片下载',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '135',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'136' => array(
		'cate_id' => '136',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '桌面',
		'cate_dir' => 'zhuomian',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '136',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'137' => array(
		'cate_id' => '137',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '手机',
		'cate_dir' => 'shouji',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '手机网站大全,批发的手机网站大全,手机网站大全wap,国外手机网站大全',
		'cate_description' => '手机网址大全是为您精心挑选出国内外最优秀的手机H网站,并保持定期更新和检查,确保您以最安全、最方便的方式,找到您的需要。',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '137',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'138' => array(
		'cate_id' => '138',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '通讯',
		'cate_dir' => 'tongxun',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '138',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'139' => array(
		'cate_id' => '139',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '相册',
		'cate_dir' => 'xiangce',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '139',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'140' => array(
		'cate_id' => '140',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '网盘',
		'cate_dir' => 'wangpan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '140',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'141' => array(
		'cate_id' => '141',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '杀毒',
		'cate_dir' => 'shadu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '141',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'142' => array(
		'cate_id' => '142',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '安全',
		'cate_dir' => 'anquan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '142',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'143' => array(
		'cate_id' => '143',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '工具',
		'cate_dir' => 'gongju',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '工具',
		'cate_description' => '工具',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '143',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'144' => array(
		'cate_id' => '144',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => 'SEO',
		'cate_dir' => 'SEO',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'SEO优化',
		'cate_description' => 'SEO优化，网站推广',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '144',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'145' => array(
		'cate_id' => '145',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '电商',
		'cate_dir' => 'retailers',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电商',
		'cate_description' => '电商',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '145',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'146' => array(
		'cate_id' => '146',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '存储',
		'cate_dir' => 'cunchu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '免费云存储，图片云存储、音频云存储、视频云存储等',
		'cate_description' => '提供安全、稳定、高速、开放的文件云存储、图片云存储、音频云存储、视频云存储等各类专业的云存储服务',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '146',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'147' => array(
		'cate_id' => '147',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '微信',
		'cate_dir' => 'weixin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '微信，微信公众号，微信公众账号，微信公众，微信公众帐号，微信网页版，网页版微信，微信网页，网页微信，微信推广，微信公众平台，微信公众平台登录，微信pc版，微信电脑版，微信群，微信推广，微信导航，微信营',
		'cate_description' => '微信公众帐号大全收录了微信公共账号,微信美女账号,微信明星帐号,微信搞笑账号等各种微信公众账号以及微信、微信网页版的使用方法。草谷子是微信公众账号推广最好的平台',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '147',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'148' => array(
		'cate_id' => '148',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '支付',
		'cate_dir' => 'zhifu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '148',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'149' => array(
		'cate_id' => '149',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => 'b2b',
		'cate_dir' => 'b2b',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'B2B网站大全,b2b,b2b网站',
		'cate_description' => 'B2B网站大全收集了企业营销人员最常用的B2B信息发布网站并对B2B网站进行了详细的介绍,其中B2B网站排名是对国内B2B网站科学排列,欢迎使用B2B网站大全!',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '149',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'150' => array(
		'cate_id' => '150',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '黑客',
		'cate_dir' => 'hacker',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '黑客网站,黑客网站大全,黑客网站排名,免费黑客网站',
		'cate_description' => '黑客网站大全为您提供：黑客网站、黑客网站大全、黑客网站排名、免费黑客网站、以及国内国外的黑客网站大全！',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '150',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'151' => array(
		'cate_id' => '151',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '数据',
		'cate_dir' => 'shuju',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '数据,数据网站,数据网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '151',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'152' => array(
		'cate_id' => '152',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => 'VR',
		'cate_dir' => 'vr',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'vr,虚拟现实,vr网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '152',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'153' => array(
		'cate_id' => '153',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '创业',
		'cate_dir' => 'chuangye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '创业网站,创业网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '153',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'154' => array(
		'cate_id' => '154',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => '交易',
		'cate_dir' => 'jiaoyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '154',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'174' => array(
		'cate_id' => '174',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '门户',
		'cate_dir' => 'menhu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '174',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'175' => array(
		'cate_id' => '175',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '酷站',
		'cate_dir' => 'kuzhan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '175',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'176' => array(
		'cate_id' => '176',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '政府',
		'cate_dir' => 'zhengfu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '176',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'177' => array(
		'cate_id' => '177',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '法律',
		'cate_dir' => 'falv',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '177',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'178' => array(
		'cate_id' => '178',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '行业',
		'cate_dir' => 'hangye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '178',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'179' => array(
		'cate_id' => '179',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '国外',
		'cate_dir' => 'guowai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '179',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'180' => array(
		'cate_id' => '180',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '宗教',
		'cate_dir' => 'zongjiao',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '180',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'181' => array(
		'cate_id' => '181',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '公益',
		'cate_dir' => 'gongyi',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '181',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'182' => array(
		'cate_id' => '182',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '税务',
		'cate_dir' => 'shuiwu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '税务',
		'cate_description' => '税务',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '182',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'183' => array(
		'cate_id' => '183',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '社会',
		'cate_dir' => 'shehui',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '社会',
		'cate_description' => '社会',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '183',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'184' => array(
		'cate_id' => '184',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '民俗',
		'cate_dir' => 'minsu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '民俗',
		'cate_description' => '民俗',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '184',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'185' => array(
		'cate_id' => '185',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '绘画',
		'cate_dir' => 'huihua',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '绘画',
		'cate_description' => '绘画',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '185',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'186' => array(
		'cate_id' => '186',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '艺术',
		'cate_dir' => 'yishu',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '艺术',
		'cate_description' => '艺术',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '186',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'187' => array(
		'cate_id' => '187',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '农业',
		'cate_dir' => 'nongye',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '农业,农业网站大全,中国农业网站大全,农业网址导航,农业网站',
		'cate_description' => '农业网站大全汇集水果、蔬菜、畜牧、水产、种子、化肥、饲料、农机、花木、粮油、茶叶、农药等农产品、农资的企业、行业或政府网站，为中国农业服务。农业网址大全，农业网站大全，农业网址导航，农业网站，农业网站导航，农业网，中国农业网。',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '187',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'188' => array(
		'cate_id' => '188',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '医药',
		'cate_dir' => 'yaopin',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '188',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'189' => array(
		'cate_id' => '189',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '香港',
		'cate_dir' => 'hk',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '香港网站,香港网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '189',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'190' => array(
		'cate_id' => '190',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '交通',
		'cate_dir' => 'jiaotong',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '交通/汽车',
		'cate_description' => '交通/汽车',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '190',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'191' => array(
		'cate_id' => '191',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '医院',
		'cate_dir' => 'yiyuan',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '191',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'192' => array(
		'cate_id' => '192',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '左派',
		'cate_dir' => 'zuopai',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '左派,左派网站,左派网站大全,左派网址大全',
		'cate_description' => '左派网站大全主张追求正义,流芳百世,最受中华爱国人士欢迎！收录各类左派网站,右派网站,社会人生网站,公益网站,工人网站,左派右派名家博客等。',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '192',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'193' => array(
		'cate_id' => '193',
		'root_id' => '8',
		'cate_mod' => 'webdir',
		'cate_name' => '国外网站',
		'cate_dir' => 'gwwz',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '国外网站,国外站点',
		'cate_description' => '收录国外优秀网站',
		'cate_arrparentid' => '0,8',
		'cate_arrchildid' => '193',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'225' => array(
		'cate_id' => '225',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '考试',
		'cate_dir' => 'kaoshi_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '225',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'226' => array(
		'cate_id' => '226',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '招生',
		'cate_dir' => 'zhaosheng_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '226',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'227' => array(
		'cate_id' => '227',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '图书',
		'cate_dir' => 'book_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '227',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'228' => array(
		'cate_id' => '228',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '外语',
		'cate_dir' => 'waiyu_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '228',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'229' => array(
		'cate_id' => '229',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '留学',
		'cate_dir' => 'liuxue_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '229',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'230' => array(
		'cate_id' => '230',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '翻译',
		'cate_dir' => 'fanyi_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '230',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'231' => array(
		'cate_id' => '231',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '招聘',
		'cate_dir' => 'zhaopin_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '招聘网站大全,惠州招聘网站大全,上海招聘网站大全,沈阳招聘网站大全,免费招聘网站大全,长春招聘网站大全,宁波招聘网站大全',
		'cate_description' => '招聘网站大全收录整理了中国各地区的人才网、招聘网、求职网、人才市场等人才招聘相关的网站信息,方便职场人士快速查找专业的招聘网站大全、求职网站大全.',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '231',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'232' => array(
		'cate_id' => '232',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '职教',
		'cate_dir' => 'zhijiao_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '232',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'233' => array(
		'cate_id' => '233',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '论文',
		'cate_dir' => 'lunwen_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '233',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'234' => array(
		'cate_id' => '234',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '百科',
		'cate_dir' => 'baike_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '234',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'235' => array(
		'cate_id' => '235',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '文库',
		'cate_dir' => 'wenku_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '235',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'236' => array(
		'cate_id' => '236',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '知识',
		'cate_dir' => 'zhishi_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '236',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'237' => array(
		'cate_id' => '237',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '天文',
		'cate_dir' => 'tianwen_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '237',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'238' => array(
		'cate_id' => '238',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '历史',
		'cate_dir' => 'lishi_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '238',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'239' => array(
		'cate_id' => '239',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '驾校',
		'cate_dir' => 'jiaxiao_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '驾校',
		'cate_description' => '驾校',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '239',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'240' => array(
		'cate_id' => '240',
		'root_id' => '6',
		'cate_mod' => 'webdir',
		'cate_name' => '科技',
		'cate_dir' => 'keji_1752375341',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '科技,科技网站,科技网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,6',
		'cate_arrchildid' => '240',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'241' => array(
		'cate_id' => '241',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '军事',
		'cate_dir' => 'junshi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '军事网站,军事网站大全,中国军事网站,军事网站导航',
		'cate_description' => '国内目前最全面,最权威的军事网址大全,最新的军事热点新闻,最全的军事热点专题,最给力的军事热帖,最精彩的军事图片,军事热点事件,军事网站大全,军事相关资源等',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '241',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'242' => array(
		'cate_id' => '242',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '财经',
		'cate_dir' => 'caijin_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '242',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'243' => array(
		'cate_id' => '243',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '证券',
		'cate_dir' => 'zhengquan_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '243',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'244' => array(
		'cate_id' => '244',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '基金',
		'cate_dir' => 'jijin_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '244',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'245' => array(
		'cate_id' => '245',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '银行',
		'cate_dir' => 'yinhang_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '245',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'246' => array(
		'cate_id' => '246',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '保险',
		'cate_dir' => 'baoxian_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '246',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'247' => array(
		'cate_id' => '247',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '地产',
		'cate_dir' => 'fangdichan_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '247',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'248' => array(
		'cate_id' => '248',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '汽车',
		'cate_dir' => 'qiche_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '248',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'249' => array(
		'cate_id' => '249',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '家居',
		'cate_dir' => 'jiaju_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '249',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'250' => array(
		'cate_id' => '250',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '购物',
		'cate_dir' => 'gouwu_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '250',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'251' => array(
		'cate_id' => '251',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '团购',
		'cate_dir' => 'gouwu_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '251',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'252' => array(
		'cate_id' => '252',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '导购',
		'cate_dir' => 'daogou_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '252',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'253' => array(
		'cate_id' => '253',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '生活',
		'cate_dir' => 'shenghuo_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '253',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'254' => array(
		'cate_id' => '254',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '天气',
		'cate_dir' => 'tianqi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '254',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'255' => array(
		'cate_id' => '255',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '地图',
		'cate_dir' => 'ditu_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '255',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'256' => array(
		'cate_id' => '256',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '旅游',
		'cate_dir' => 'lvyou_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '256',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'257' => array(
		'cate_id' => '257',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '机票',
		'cate_dir' => 'jipiao_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '257',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'258' => array(
		'cate_id' => '258',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '酒店',
		'cate_dir' => 'jiudian_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '258',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'259' => array(
		'cate_id' => '259',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '美食',
		'cate_dir' => 'meishi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '259',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'260' => array(
		'cate_id' => '260',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '健康',
		'cate_dir' => 'jiankang_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '260',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'261' => array(
		'cate_id' => '261',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '母婴',
		'cate_dir' => 'muying_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '261',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'262' => array(
		'cate_id' => '262',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '女性',
		'cate_dir' => 'nvxing_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '262',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'263' => array(
		'cate_id' => '263',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '男士',
		'cate_dir' => 'nanshi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '263',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'264' => array(
		'cate_id' => '264',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '时尚',
		'cate_dir' => 'shishang_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '264',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'265' => array(
		'cate_id' => '265',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '美容',
		'cate_dir' => 'meirong_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '265',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'266' => array(
		'cate_id' => '266',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '减肥',
		'cate_dir' => 'jianfei_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '266',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'267' => array(
		'cate_id' => '267',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '报纸',
		'cate_dir' => 'baozhi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '267',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'268' => array(
		'cate_id' => '268',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '电视',
		'cate_dir' => 'dianshi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '268',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'269' => array(
		'cate_id' => '269',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '杂志',
		'cate_dir' => 'zazhi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '269',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'270' => array(
		'cate_id' => '270',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '宠物',
		'cate_dir' => 'chongwu_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '270',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'271' => array(
		'cate_id' => '271',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '借贷',
		'cate_dir' => 'jiedai_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '借贷',
		'cate_description' => '借贷',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '271',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'272' => array(
		'cate_id' => '272',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '奢侈',
		'cate_dir' => 'shechi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '奢侈品',
		'cate_description' => '奢侈品',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '272',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'273' => array(
		'cate_id' => '273',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '婚庆',
		'cate_dir' => 'hunqing_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '婚庆',
		'cate_description' => '婚庆',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '273',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'274' => array(
		'cate_id' => '274',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '鲜花',
		'cate_dir' => 'xianhua_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '鲜花',
		'cate_description' => '鲜花',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '274',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'275' => array(
		'cate_id' => '275',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '眼镜',
		'cate_dir' => 'yanjing_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '眼镜',
		'cate_description' => '眼镜',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '275',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'276' => array(
		'cate_id' => '276',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '珠宝',
		'cate_dir' => 'zhubao_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '珠宝',
		'cate_description' => '珠宝',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '276',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'277' => array(
		'cate_id' => '277',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '租赁',
		'cate_dir' => 'zulin_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '租赁',
		'cate_description' => '租赁',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '277',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'278' => array(
		'cate_id' => '278',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '保健',
		'cate_dir' => 'baojian_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '278',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'279' => array(
		'cate_id' => '279',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '物流',
		'cate_dir' => 'kuaidi_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '物流',
		'cate_description' => '物流',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '279',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'280' => array(
		'cate_id' => '280',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '起名',
		'cate_dir' => 'qiming_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '星座/起名',
		'cate_description' => '星座/起名',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '280',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'281' => array(
		'cate_id' => '281',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '搬家',
		'cate_dir' => 'banjia_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搬家',
		'cate_description' => '搬家',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '281',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'282' => array(
		'cate_id' => '282',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '分类信息',
		'cate_dir' => 'fenlei_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '分类信息网站,分类信息网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '282',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'283' => array(
		'cate_id' => '283',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '机车',
		'cate_dir' => 'jiche_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '机车,机车网',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '283',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'284' => array(
		'cate_id' => '284',
		'root_id' => '4',
		'cate_mod' => 'webdir',
		'cate_name' => '海淘',
		'cate_dir' => 'haitao_1752375481',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '海淘网站,海淘网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,4',
		'cate_arrchildid' => '284',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'285' => array(
		'cate_id' => '285',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '视频',
		'cate_dir' => 'shipin_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电影,视频,电影网,网络电视,在线影院',
		'cate_description' => '为您提供电视剧、电影、动漫、综艺节目网站，推荐和排行榜并可在线免费观看百度影音伦理电影、动作片、喜剧片、爱情片、搞笑片高清下载等,高清视频电影网站尽在点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '285',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'286' => array(
		'cate_id' => '286',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '音乐',
		'cate_dir' => 'yinyue_1752375584',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => '音乐,流行歌曲,好听的歌,在线听歌,歌曲',
		'cate_description' => '音乐网站大全 - 我们为您搜集流行歌曲、好听的歌、mp3下载、mp3歌曲、在线听歌、网络歌曲、非主流音乐等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '286',
		'cate_childcount' => '0',
		'cate_postcount' => '3'
	),
	'287' => array(
		'cate_id' => '287',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '小说',
		'cate_dir' => 'xiaoshuo_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '小说,免费小说,热门小说',
		'cate_description' => '小说网站，最全的小说网站大全，热门小说站点排行榜，小说分类大全，小说类贴吧，免费小说精选，提供给您最全面的小说阅读',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '287',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'288' => array(
		'cate_id' => '288',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '搞笑',
		'cate_dir' => 'gaoxiao_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '搞笑,嘻嘻哈哈,腾讯笑话,来福岛,八目妖,糗事百科,搞搞吧',
		'cate_description' => '搞笑网站大全，最热的爆笑笑话、搞笑图片、动态图、冷笑话、糗事笑话、成人笑话、经典笑话、内涵段子等笑话网址大全',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '288',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'289' => array(
		'cate_id' => '289',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '美图',
		'cate_dir' => 'meitu_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '美图,唯美图片,美图网站,美图网站大全',
		'cate_description' => '美图网站大全 - 我们为您搜集欧美图片、唯美图片、欧美图库、美图看看、优美图片、精美图片、美图录等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '289',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'290' => array(
		'cate_id' => '290',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '星座',
		'cate_dir' => 'xingzuo_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '星座网站,星座查询,十二星座,星座配对',
		'cate_description' => '星座网站大全 - 我们为您搜集今日运势查询、星座运势2015年、星座运势、美国神婆、神婆网、十二星座、星座查询、星座配对、星座爱情、星座排行、星座之最、星座性格分析等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '290',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'291' => array(
		'cate_id' => '291',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => 'QQ',
		'cate_dir' => 'qq_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'qq网站,qq技术网站,qq头像网站,qq个性网站',
		'cate_description' => 'QQ网站大全 - 我们为您搜集qq网站、qq技术网站、qq头像网站、qq个性网站、qq团购网站等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '291',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'292' => array(
		'cate_id' => '292',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '交友',
		'cate_dir' => 'jiaoyou_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '交友网站,同城交友网站,免费交友网站,交友网站大全',
		'cate_description' => '交友网站是基于网络平台的广泛性、互通性、娱乐性、经济性、安全性等优点，于本世纪初出现在网络交流方式中的互动型服务网站。按照类型，可以简单的将它们分为婚恋交友类网站和社交交友类网站两种。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '292',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'293' => array(
		'cate_id' => '293',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '社区',
		'cate_dir' => 'shequ_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '社交网站,sns社交网站,社交平台,社交网',
		'cate_description' => '全称Social Network Site，即\"社交网站\"或\"社交网\"。注意要与\"社区网站\"区分，两者有本质区别。社会性网络（Social Networking）是指个人之间的关系网络，这种基于社会网络关系系统思想的网站就是社会性网络网站(SNS网站)。SNS的全称也可以是Social Networking Services，即社会性网络服务，专指旨在帮助人们建立社会性网络的互联网应用服务。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '293',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'294' => array(
		'cate_id' => '294',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '体育',
		'cate_dir' => 'tiyu_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '体育,体育网站大全,体育网站',
		'cate_description' => '体育（physical education，缩写PE或P.E.），是一种复杂的社会文化现象，它以身体与智力活动为基本手段，根据人体生长发育、技能形成和机能提高等规律，达到促进全面发育、提高身体素质与全面教育水平、增强体质与提高运动能力、改善生活方式与提高生活质量的一种有意识、有目的、有组织的社会活动。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '294',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'295' => array(
		'cate_id' => '295',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => 'NBA',
		'cate_dir' => 'nba_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => 'nba录像,NBA直播,nba中文网,NBA',
		'cate_description' => 'NBA是美国男子职业篮球联赛（National Basketball Association）的简称，于1946年6月6日在纽约成立，是由北美三十支队伍组成的男子职业篮球联盟，美国四大职业体育联盟之一。其中诞生了迈克尔·乔丹、科比·布莱恩特、勒布朗·詹姆斯等球星，是世界上水平最高的篮球赛事。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '295',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'296' => array(
		'cate_id' => '296',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '摄影',
		'cate_dir' => 'sheying_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '摄影,婚纱摄影,写真摄影,儿童摄影,服装摄影',
		'cate_description' => '摄影一词是源于希腊语 φῶς phos（光线）和 γραφι graphis（绘画、绘图）或γραφή graphê，两字一起的意思是\"以光线绘图\"。摄影是指使用某种专门设备进行影像记录的过程，一般我们使用机械照相机或者数码照相机进行摄影。有时摄影也会被称为照相，也就是通过物体所反射的光线使感光介质曝光的过程。有人说过的一句精辟的语言：摄影家的能力是把日常生活中稍纵即逝的平凡事物转化为不朽的视觉图像。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '296',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'297' => array(
		'cate_id' => '297',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '棋牌',
		'cate_dir' => 'qipai_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '棋牌,棋牌游戏,网络棋牌,真人棋牌游戏',
		'cate_description' => '棋牌是棋类和牌类娱乐项目的总称，包括中国象棋、围棋、国际象棋、蒙古象棋、五子棋、跳棋、国际跳棋（已列入首届世界智力运动会项目）、军棋、桥牌、扑克、麻将等等诸多传统或新兴娱乐项目。棋牌是十分有趣味的娱乐活动，很多人为此废寝忘食，这种过度沉迷于其中的做法是极不健康的。要使下棋打牌完全为养生所用，先了解其中的禁忌是十分必要的。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '297',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'298' => array(
		'cate_id' => '298',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '曲艺',
		'cate_dir' => 'quyi_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '曲艺,微曲艺曲,艺杂谈,曲艺网',
		'cate_description' => '曲艺是中华民族各种\"说唱艺术\"的统称，它是由民间口头文学和歌唱艺术经过长期发展演变形成的一种独特的艺术形式。据不完全统计，至今活在中国民间的各族曲艺曲种约有400个左右。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '298',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'299' => array(
		'cate_id' => '299',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '爱好',
		'cate_dir' => 'aihao_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '299',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'300' => array(
		'cate_id' => '300',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '娱乐',
		'cate_dir' => 'yule_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '娱乐,娱乐网站,日本娱乐网站,韩国娱乐网站,中国娱乐网站',
		'cate_description' => '娱乐网站大全 - 我们为您搜集香港娱乐网站、高端娱乐网站、台湾娱乐网站、网上娱乐网站、24小时娱乐网站等等，找网站就来点我网站目录。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '300',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'301' => array(
		'cate_id' => '301',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '直播',
		'cate_dir' => 'zhibo_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '直播,直播网站,韩国直播网站,足球直播网站,lol直播网站',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '301',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'302' => array(
		'cate_id' => '302',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => 'dj',
		'cate_dir' => 'dj_1752375584',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'dj,dj网站,dj网站大全',
		'cate_description' => 'DJ网站大全提供：潮牌夜店dj,dj舞曲 超劲爆dj网站,提供最新dj下载,歌曲串烧,嗨曲,dj小可电音舞曲,慢摇dj,mc喊麦dj网站,夜店歌曲,最好听的dj嗨嗨网音乐MP3舞曲,车载dj舞曲等等有关DJ的网站让大家选择。',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '302',
		'cate_childcount' => '0',
		'cate_postcount' => '2'
	),
	'303' => array(
		'cate_id' => '303',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '小游戏',
		'cate_dir' => 'xiaoyouxi_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '小游戏网站,小游戏大全,小游戏网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '303',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'304' => array(
		'cate_id' => '304',
		'root_id' => '5',
		'cate_mod' => 'webdir',
		'cate_name' => '电影',
		'cate_dir' => 'dianying_1752375584',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电影,电影网站,电影网站大全',
		'cate_description' => '',
		'cate_arrparentid' => '0,5',
		'cate_arrchildid' => '304',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'305' => array(
		'cate_id' => '305',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '礼品',
		'cate_dir' => 'lipin_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '广告礼品网，广告礼品大全，创意礼品',
		'cate_description' => '95分类目录为您提供大量广告礼品信息，您可以免费查询广告礼品,广告礼品定制,广告礼品回收,广告礼品收购,广告礼品批发,广告礼品网等信息，同时您可以免费发布广告礼品信息',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '305',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'306' => array(
		'cate_id' => '306',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '服务',
		'cate_dir' => 'fuwu_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '礼仪公司服务项目，实用礼仪大全',
		'cate_description' => '95分类目录为您提供优质高效的礼仪庆典服务实用礼仪网站大全',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '306',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'307' => array(
		'cate_id' => '307',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '配件',
		'cate_dir' => 'peijian_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '模具配件供应商，模具配件厂',
		'cate_description' => '模具配件点我网站大全，点我网航提供模具配件网址大全，收集和整理模具配件点我网站大全列表信息，让用户最快的找到模具配件自己所需模具配件网址。更多的模具配件网站尽在95分类目录。',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '307',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'308' => array(
		'cate_id' => '308',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '工艺',
		'cate_dir' => 'gongyi_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '公司与企业，艺礼品，排行榜',
		'cate_description' => '工艺礼品,金属工艺制品厂排行榜,聚合了众多优质礼品公司与工艺礼品厂，并根据网站的综合值进行排名.找最好的公司与，广告礼品厂家定做',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '308',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'309' => array(
		'cate_id' => '309',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '工业',
		'cate_dir' => 'gongye_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '工业用品，公司与企业，排行榜',
		'cate_description' => '工业用品网址大全，收集和整理工业用品网站信息，让用户最快的找到工业用品自己所需机械设备网址',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '309',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
	'310' => array(
		'cate_id' => '310',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '辅料',
		'cate_dir' => 'fuliao_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '服装辅料，服装辅料知名门户，辅料网，服装辅料网，服装辅料资讯，中国辅料网，服装辅料采购，服装行业展会，服装辅料企业合作，服装辅料商城，服饰配件，织带厂',
		'cate_description' => '点我网站目录收集和整理供应批发、求购、辅料企业、辅料行业资讯、辅料行业展会等信息服务网站，让用户最快的找到自己所需服装辅料网址',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '310',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'311' => array(
		'cate_id' => '311',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '电气',
		'cate_dir' => 'dianqi_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电气/电工',
		'cate_description' => '电气/电工',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '311',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'312' => array(
		'cate_id' => '312',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '机械',
		'cate_dir' => 'jixie_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '机械设备，机械设备网，机械设备有限公司，机械设备厂家',
		'cate_description' => '机械设备云目录，整理和收集网站网址，方便和快速的找到机械设备网站大全信息，更多的相关机械设备有限公司网站尽在分类目录',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '312',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'313' => array(
		'cate_id' => '313',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '家具',
		'cate_dir' => 'jiajupin_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '家具品牌官网，家具网上商城，原木家具，高档家具品牌有哪些',
		'cate_description' => '家私家具云目录，整理和收集网站网址，方便和快速的找到家私家具网站大全信息，更多的相关家私家具网站尽在分类目录',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '313',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'314' => array(
		'cate_id' => '314',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '建筑',
		'cate_dir' => 'jianzhu_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '建筑设计，建筑装饰装修，建筑装饰材料，建筑装修装饰工程',
		'cate_description' => '建筑装饰云目录，整理和收集网站网址，方便和快速的找到建筑装饰网站大全信息，更多的相关建筑装饰网站尽在分类目录!',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '314',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'315' => array(
		'cate_id' => '315',
		'root_id' => '7',
		'cate_mod' => 'webdir',
		'cate_name' => '电子',
		'cate_dir' => 'dianzi_1752375675',
		'cate_url' => '',
		'cate_isbest' => '0',
		'cate_keywords' => '电子与通信,电子与通信工程排名',
		'cate_description' => '电子与通信云目录，整理和收集网',
		'cate_arrparentid' => '0,7',
		'cate_arrchildid' => '315',
		'cate_childcount' => '0',
		'cate_postcount' => '0'
	),
	'316' => array(
		'cate_id' => '316',
		'root_id' => '3',
		'cate_mod' => 'webdir',
		'cate_name' => 'CDN',
		'cate_dir' => 'CDN',
		'cate_url' => '',
		'cate_isbest' => '1',
		'cate_keywords' => 'CDN网站',
		'cate_description' => 'CDN网站分类目录',
		'cate_arrparentid' => '0,3',
		'cate_arrchildid' => '316',
		'cate_childcount' => '0',
		'cate_postcount' => '1'
	),
);
?>