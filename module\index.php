<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '网站首页';
$pageurl = '?mod=index';
$tempfile = 'index.html';

if (!$smarty->isCached($tempfile)) {
	$smarty->assign('site_title', $options['site_title']);
	$smarty->assign('site_keywords', $options['site_keywords']);
	$smarty->assign('site_description', $options['site_description']);
	$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
	$smarty->assign('site_rss', get_rssfeed());

	// 获取音乐列表数据
	try {
		$music_links = get_music_links(286, 10);
		$smarty->assign('music_links', $music_links);
	} catch (Exception $e) {
		// 如果获取失败，传递空数组
		$smarty->assign('music_links', array());
	}
}

smarty_output($tempfile);
?>