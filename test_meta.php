<?php
// Meta抓取加强测试页面
define('IN_IWEBDIR', true);
require_once('source/module/webdata.php');

// 扩展测试域名列表，包含各种类型的网站
$test_domains = array(
    // 国内网站
    'www.baidu.com',
    'www.taobao.com',
    'www.jd.com',
    'www.sina.com.cn',
    'www.163.com',

    // 国外网站
    'www.google.com',
    'github.com',
    'stackoverflow.com',
    'www.wikipedia.org',
    'www.reddit.com',

    // 特殊情况测试
    'httpbin.org',           // HTTP测试
    'example.com',           // 简单页面
    'www.nonexistentdomain12345.com', // 不存在的域名
);

echo "<h2>Meta抓取功能测试</h2>";
echo "<style>
    .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
</style>";

foreach ($test_domains as $domain) {
    echo "<div class='test-result'>";
    echo "<h3>测试域名: $domain</h3>";
    
    $start_time = microtime(true);
    
    try {
        $meta = get_sitemeta($domain);
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        
        if (!empty($meta['title']) || !empty($meta['keywords']) || !empty($meta['description'])) {
            echo "<div class='success'>";
            echo "<strong>✓ 抓取成功</strong> (耗时: {$duration}ms)<br>";
            echo "<strong>标题:</strong> " . htmlspecialchars($meta['title']) . "<br>";
            echo "<strong>关键词:</strong> " . htmlspecialchars($meta['keywords']) . "<br>";
            echo "<strong>描述:</strong> " . htmlspecialchars($meta['description']) . "<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<strong>✗ 抓取失败</strong> (耗时: {$duration}ms) - 未获取到任何Meta信息<br>";
            echo "</div>";
        }
    } catch (Exception $e) {
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        echo "<div class='error'>";
        echo "<strong>✗ 抓取异常</strong> (耗时: {$duration}ms)<br>";
        echo "<strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // 避免请求过于频繁
    sleep(1);
}

echo "<div class='info'>";
echo "<h3>加强优化说明</h3>";
echo "<ul>";
echo "<li>✓ 多协议尝试策略 (HTTPS → HTTP)</li>";
echo "<li>✓ 多域名格式尝试 (www前缀处理)</li>";
echo "<li>✓ 增强的正则表达式支持更多Meta标签格式</li>";
echo "<li>✓ 智能编码检测和转换</li>";
echo "<li>✓ 多种User-Agent轮换</li>";
echo "<li>✓ 分层抓取策略 (Range请求 → 完整请求)</li>";
echo "<li>✓ 内容类型检测</li>";
echo "<li>✓ 备用内容提取 (从h1、p标签提取)</li>";
echo "<li>✓ 完善的错误处理和日志记录</li>";
echo "<li>✓ 特殊字符清理和长度限制</li>";
echo "</ul>";
echo "</div>";

// 显示一些调试信息
echo "<div class='info'>";
echo "<h3>系统信息</h3>";
echo "<p><strong>CURL版本:</strong> " . (function_exists('curl_version') ? curl_version()['version'] : '不可用') . "</p>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>支持的编码:</strong> " . implode(', ', mb_list_encodings()) . "</p>";
echo "</div>";
?>
