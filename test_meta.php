<?php
// Meta抓取测试页面
define('IN_IWEBDIR', true);
require_once('source/module/webdata.php');

// 测试域名列表
$test_domains = array(
    'www.baidu.com',
    'www.google.com',
    'github.com',
    'stackoverflow.com',
    'www.taobao.com'
);

echo "<h2>Meta抓取功能测试</h2>";
echo "<style>
    .test-result { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .info { background-color: #d1ecf1; border-color: #bee5eb; }
</style>";

foreach ($test_domains as $domain) {
    echo "<div class='test-result'>";
    echo "<h3>测试域名: $domain</h3>";
    
    $start_time = microtime(true);
    
    try {
        $meta = get_sitemeta($domain);
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        
        if (!empty($meta['title']) || !empty($meta['keywords']) || !empty($meta['description'])) {
            echo "<div class='success'>";
            echo "<strong>✓ 抓取成功</strong> (耗时: {$duration}ms)<br>";
            echo "<strong>标题:</strong> " . htmlspecialchars($meta['title']) . "<br>";
            echo "<strong>关键词:</strong> " . htmlspecialchars($meta['keywords']) . "<br>";
            echo "<strong>描述:</strong> " . htmlspecialchars($meta['description']) . "<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<strong>✗ 抓取失败</strong> (耗时: {$duration}ms) - 未获取到任何Meta信息<br>";
            echo "</div>";
        }
    } catch (Exception $e) {
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        echo "<div class='error'>";
        echo "<strong>✗ 抓取异常</strong> (耗时: {$duration}ms)<br>";
        echo "<strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
        echo "</div>";
    }
    
    echo "</div>";
    
    // 避免请求过于频繁
    sleep(1);
}

echo "<div class='info'>";
echo "<h3>优化说明</h3>";
echo "<ul>";
echo "<li>✓ 添加了30秒前端超时控制</li>";
echo "<li>✓ 添加了完善的错误处理和用户提示</li>";
echo "<li>✓ 后端Meta抓取超时时间优化为10秒</li>";
echo "<li>✓ 添加了按钮状态管理，防止重复点击</li>";
echo "<li>✓ 优化了正则表达式，支持更多Meta标签格式</li>";
echo "<li>✓ 添加了内容长度限制和特殊字符转义</li>";
echo "<li>✓ 添加了详细的日志记录用于调试</li>";
echo "</ul>";
echo "</div>";
?>
