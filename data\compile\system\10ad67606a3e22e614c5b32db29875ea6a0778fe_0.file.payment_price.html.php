<?php
/* Smarty version 4.5.5, created on 2025-07-16 17:07:20
  from '/www/wwwroot/www.95dir.com/themes/system/payment_price.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_68776bc8714b49_44488149',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '10ad67606a3e22e614c5b32db29875ea6a0778fe' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/payment_price.html',
      1 => 1752656828,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_68776bc8714b49_44488149 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="main">
    <div class="title">
        <h2>价格配置管理</h2>
        <div class="nav">
            <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
" class="btn">配置列表</a>
            <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=add" class="btn">添加配置</a>
        </div>
    </div>
    
    <?php if ($_smarty_tpl->tpl_vars['h_action']->value == 'add') {?>
    <!-- 添加价格配置表单 -->
    <div class="form">
        <form method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
            <input type="hidden" name="act" value="saveadd">
            <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
                <tr bgcolor="#E7E7E7">
                    <th colspan="2">添加价格配置</th>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td width="120">服务类型：</td>
                    <td>
                        <select name="service_type" required>
                            <option value="">请选择服务类型</option>
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['service_types']->value, 'type_name', false, 'type_id');
$_smarty_tpl->tpl_vars['type_name']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['type_id']->value => $_smarty_tpl->tpl_vars['type_name']->value) {
$_smarty_tpl->tpl_vars['type_name']->do_else = false;
?>
                            <option value="<?php echo $_smarty_tpl->tpl_vars['type_id']->value;?>
"><?php echo $_smarty_tpl->tpl_vars['type_name']->value;?>
</option>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务名称：</td>
                    <td><input type="text" name="service_name" size="30" maxlength="50" required placeholder="如：VIP服务"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>价格：</td>
                    <td><input type="number" name="price" step="0.01" min="0" required placeholder="0.00"> 元</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>计费单位：</td>
                    <td>
                        <select name="unit" required>
                            <option value="">请选择单位</option>
                            <option value="年">年</option>
                            <option value="月">月</option>
                            <option value="次">次</option>
                            <option value="天">天</option>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务时长：</td>
                    <td><input type="number" name="duration_days" min="0" placeholder="0"> 天 <span style="color: #999;">（0表示不限时长，如快审服务）</span></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效日期：</td>
                    <td><input type="date" name="effective_date" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效时间：</td>
                    <td><input type="time" name="effective_time" value="00:00"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>备注：</td>
                    <td><textarea name="remark" rows="3" cols="50" placeholder="可选"></textarea></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td colspan="2" align="center">
                        <input type="submit" value="添加配置" class="btn">
                        <input type="button" value="返回" class="btn" onclick="location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
'">
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <?php } elseif ($_smarty_tpl->tpl_vars['h_action']->value == 'saveedit') {?>
    <!-- 编辑价格配置表单 -->
    <div class="form">
        <form method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
            <input type="hidden" name="act" value="saveedit">
            <input type="hidden" name="id" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['id'];?>
">
            <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
                <tr bgcolor="#E7E7E7">
                    <th colspan="2">编辑价格配置</th>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td width="120">服务类型：</td>
                    <td>
                        <select name="service_type" required>
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['service_types']->value, 'type_name', false, 'type_id');
$_smarty_tpl->tpl_vars['type_name']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['type_id']->value => $_smarty_tpl->tpl_vars['type_name']->value) {
$_smarty_tpl->tpl_vars['type_name']->do_else = false;
?>
                            <option value="<?php echo $_smarty_tpl->tpl_vars['type_id']->value;?>
" <?php if ($_smarty_tpl->tpl_vars['price_config']->value['service_type'] == $_smarty_tpl->tpl_vars['type_id']->value) {?>selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['type_name']->value;?>
</option>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务名称：</td>
                    <td><input type="text" name="service_name" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['service_name'];?>
" size="30" maxlength="50" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>价格：</td>
                    <td><input type="number" name="price" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['price'];?>
" step="0.01" min="0" required> 元</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>计费单位：</td>
                    <td>
                        <select name="unit" required>
                            <option value="年" <?php if ($_smarty_tpl->tpl_vars['price_config']->value['unit'] == '年') {?>selected<?php }?>>年</option>
                            <option value="月" <?php if ($_smarty_tpl->tpl_vars['price_config']->value['unit'] == '月') {?>selected<?php }?>>月</option>
                            <option value="次" <?php if ($_smarty_tpl->tpl_vars['price_config']->value['unit'] == '次') {?>selected<?php }?>>次</option>
                            <option value="天" <?php if ($_smarty_tpl->tpl_vars['price_config']->value['unit'] == '天') {?>selected<?php }?>>天</option>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务时长：</td>
                    <td><input type="number" name="duration_days" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['duration_days'];?>
" min="0"> 天</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效日期：</td>
                    <td><input type="date" name="effective_date" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['effective_date'];?>
" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效时间：</td>
                    <td><input type="time" name="effective_time" value="<?php echo $_smarty_tpl->tpl_vars['price_config']->value['effective_hour'];?>
"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>备注：</td>
                    <td><textarea name="remark" rows="3" cols="50"><?php echo $_smarty_tpl->tpl_vars['price_config']->value['remark'];?>
</textarea></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td colspan="2" align="center">
                        <input type="submit" value="保存修改" class="btn">
                        <input type="button" value="返回" class="btn" onclick="location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
'">
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <?php } else { ?>
    <!-- 当前有效价格 -->
    <div class="current-prices" style="margin-bottom: 30px;">
        <h3>当前有效价格</h3>
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="100">服务类型</th>
                <th width="120">服务名称</th>
                <th width="100">价格</th>
                <th width="80">时长</th>
                <th width="140">生效时间</th>
                <th width="100">操作员</th>
                <th>备注</th>
            </tr>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['current_prices']->value, 'price');
$_smarty_tpl->tpl_vars['price']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['price']->value) {
$_smarty_tpl->tpl_vars['price']->do_else = false;
?>
            <tr bgcolor="#FFFFFF">
                <td align="center">
                    <?php if ($_smarty_tpl->tpl_vars['price']->value['service_type'] == 1) {?><span style="color: #ff6600;">VIP</span>
                    <?php } elseif ($_smarty_tpl->tpl_vars['price']->value['service_type'] == 2) {?><span style="color: #28a745;">推荐</span>
                    <?php } elseif ($_smarty_tpl->tpl_vars['price']->value['service_type'] == 3) {?><span style="color: #007bff;">快审</span>
                    <?php }?>
                </td>
                <td><?php echo $_smarty_tpl->tpl_vars['price']->value['service_name'];?>
</td>
                <td align="center" style="color: #28a745; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['price']->value['price_display'];?>
</td>
                <td align="center"><?php if ($_smarty_tpl->tpl_vars['price']->value['duration_days'] > 0) {
echo $_smarty_tpl->tpl_vars['price']->value['duration_days'];?>
天<?php } else { ?>不限<?php }?></td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['price']->value['effective_date'];?>
</td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['price']->value['operator'];?>
</td>
                <td><?php echo $_smarty_tpl->tpl_vars['price']->value['remark'];?>
</td>
            </tr>
            <?php
}
if ($_smarty_tpl->tpl_vars['price']->do_else) {
?>
            <tr bgcolor="#FFFFFF">
                <td colspan="7" align="center" style="padding: 30px; color: #999;">暂无价格配置</td>
            </tr>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </table>
    </div>
    
    <!-- 价格历史记录 -->
    <div class="price-history">
        <h3>价格历史记录</h3>
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="60">ID</th>
                <th width="100">服务类型</th>
                <th width="120">服务名称</th>
                <th width="100">价格</th>
                <th width="80">时长</th>
                <th width="140">生效时间</th>
                <th width="80">状态</th>
                <th width="100">操作员</th>
                <th width="120">操作</th>
            </tr>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['price_history']->value, 'history');
$_smarty_tpl->tpl_vars['history']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['history']->value) {
$_smarty_tpl->tpl_vars['history']->do_else = false;
?>
            <tr bgcolor="#FFFFFF">
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['history']->value['id'];?>
</td>
                <td align="center">
                    <?php if ($_smarty_tpl->tpl_vars['history']->value['service_type'] == 1) {?><span style="color: #ff6600;">VIP</span>
                    <?php } elseif ($_smarty_tpl->tpl_vars['history']->value['service_type'] == 2) {?><span style="color: #28a745;">推荐</span>
                    <?php } elseif ($_smarty_tpl->tpl_vars['history']->value['service_type'] == 3) {?><span style="color: #007bff;">快审</span>
                    <?php }?>
                </td>
                <td><?php echo $_smarty_tpl->tpl_vars['history']->value['service_name'];?>
</td>
                <td align="center" style="color: #28a745; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['history']->value['price_display'];?>
</td>
                <td align="center"><?php if ($_smarty_tpl->tpl_vars['history']->value['duration_days'] > 0) {
echo $_smarty_tpl->tpl_vars['history']->value['duration_days'];?>
天<?php } else { ?>不限<?php }?></td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['history']->value['effective_date'];?>
</td>
                <td align="center">
                    <span style="color: <?php if ($_smarty_tpl->tpl_vars['history']->value['status']) {?>#28a745<?php } else { ?>#dc3545<?php }?>;"><?php echo $_smarty_tpl->tpl_vars['history']->value['status_text'];?>
</span>
                </td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['history']->value['operator'];?>
</td>
                <td align="center">
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=edit&id=<?php echo $_smarty_tpl->tpl_vars['history']->value['id'];?>
" class="btn" style="font-size: 12px; padding: 2px 6px;">编辑</a>
                    <?php if ($_smarty_tpl->tpl_vars['history']->value['status']) {?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=disable&id=<?php echo $_smarty_tpl->tpl_vars['history']->value['id'];?>
" onclick="return confirm('确认停用此配置？')" class="btn" style="font-size: 12px; padding: 2px 6px;">停用</a>
                    <?php } else { ?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=enable&id=<?php echo $_smarty_tpl->tpl_vars['history']->value['id'];?>
" class="btn" style="font-size: 12px; padding: 2px 6px;">启用</a>
                    <?php }?>
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=delete&id=<?php echo $_smarty_tpl->tpl_vars['history']->value['id'];?>
" onclick="return confirm('确认删除此配置？删除后不可恢复！')" class="btn" style="font-size: 12px; padding: 2px 6px; color: #dc3545;">删除</a>
                </td>
            </tr>
            <?php
}
if ($_smarty_tpl->tpl_vars['history']->do_else) {
?>
            <tr bgcolor="#FFFFFF">
                <td colspan="9" align="center" style="padding: 30px; color: #999;">暂无历史记录</td>
            </tr>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </table>
    </div>
    <?php }?>
</div>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
