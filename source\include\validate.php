<?php
function is_valid_dir($str) {
	if (preg_match('/^[a-zA-Z][a-zA-Z0-9_-]*$/', $str)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_url($url) {
	if (preg_match('/^http(s)?:\/\//i', $url)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_email($email) {
	if (preg_match('/^[\w\-\.]+@[\w\-\.]+(\.\w+)+$/', $email)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_domain($domain) {
	// 移除协议前缀
	$domain = preg_replace('/^https?:\/\//', '', $domain);
	$domain = preg_replace('/^www\./', '', $domain);
	$domain = rtrim($domain, '/');

	// 基本域名格式验证
	if (preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $domain)) {
		return true;
	} else {
		return false;
	}
}
?>