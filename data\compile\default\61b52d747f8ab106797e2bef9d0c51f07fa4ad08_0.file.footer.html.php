<?php
/* Smarty version 4.5.5, created on 2025-07-22 18:32:17
  from '/www/wwwroot/www.95dir.com/themes/default/footer.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_687f68b1992fe3_43594365',
  'has_nocache_code' => true,
  'file_dependency' => 
  array (
    '61b52d747f8ab106797e2bef9d0c51f07fa4ad08' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/footer.html',
      1 => 1753180335,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_687f68b1992fe3_43594365 (Smarty_Internal_Template $_smarty_tpl) {
?>
    <div id="footer">
        <a href="https://frogdr.com/95dir.com?utm_source=95dir.com" target="_blank" 
   style="display: block; text-align: center; margin: 0 auto 15px auto;">
    <img src="https://frogdr.com/95dir.com/badge-white.svg" 
         alt="Monitor your Domain Rating with FrogDR" 
         width="250" height="54" 
         style="display: inline-block; margin: 0 auto; vertical-align: middle;">
</a>
    	<div id="fmenu"><?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_pages(), 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?><a href="<?php echo $_smarty_tpl->tpl_vars['item']->value['page_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['item']->value['page_name'];?>
</a> | <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?><a href="?mod=update">最新收录</a> | <a href="?mod=archives">数据归档</a> | <a href="?mod=top">TOP排行榜</a> | <a href="?mod=blacklist">黑名单</a> | <a href="?mod=datastats">数据公示</a> | <a href="?mod=sitemap">站点地图</a></div>
    	<div id="fcopy"><?php echo $_smarty_tpl->tpl_vars['site_copyright']->value;?>
 | <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="nofollow" target="_blank">鄂ICP备2024062716号-1</a></div>
		<div id="fcopy"><?php echo insert_script_time(array(),$_smarty_tpl);?></div>

<!-- 在线统计和QQ群 -->
<div id="statsContainer" style="text-align: center; margin: 10px 0; font-size: 14px;">
    <div style="margin-bottom: 5px;">当前在线：<span id="onlineCount">加载中...</span>人 | 总访客：<span id="totalVisitors">加载中...</span>人</div>
    <a href="https://qun.qq.com/universal-share/share?ac=1&authKey=VD1VzovGfKEgsMPHwCRCMu6An9Er9ihrNfC88vO3Vkf0YgWW6O2OUZ6rQcFXnfXi&busi_data=eyJncm91cENvZGUiOiI4Njg4NTAyMTYiLCJ0b2tlbiI6IjZlcDhhc0srbGtNRlNrWjRkK3pVazA3blR0OGlUditUWGhYdldYdUI5N3M2VFF3dU0vMTBCSWl0c09relVZNUEiLCJ1aW4iOiIzNjMyMDk0In0=&data=3-2hnd4BuWS_RPz79Qa7DgA98Eag27Uvx7rVtiXfpes_7LoAlc5BtxTTkos3JyBmIcVTHp9ZC0K6p3qVXmJvYt__wRyd070zIKdMxpWzS_0&svctype=5&tempid=h5_group_info" target="icp_edi_blank" style="color: #007bff; text-decoration: none;">🐧群：868850216</a>
</div>
    </div>

<?php echo '<script'; ?>
>
    // 更新在线统计
    function updateOnlineStats() {
        fetch('/data/online_stats/online.php')
            .then(response => response.json())
            .then(data => {
                document.getElementById('onlineCount').textContent = data.online;
                document.getElementById('totalVisitors').textContent = data.total;
            })
            .catch(() => {
                console.log('在线统计服务不可用');
                document.getElementById('onlineCount').textContent = '0';
                document.getElementById('totalVisitors').textContent = '0';
            });
    }

    // 页面加载时立即执行
    updateOnlineStats();

    // 设置定时器 - 在线统计每60秒更新
    setInterval(updateOnlineStats, 60000);
<?php echo '</script'; ?>
><?php }
}
