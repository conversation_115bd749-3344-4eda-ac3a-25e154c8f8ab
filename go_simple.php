<?php
    // 简化版本的go.php，用于测试
    $t_url = $_GET['url'];
    $web_id = intval($_GET['id']);
    
    // 简单的出站统计（不依赖复杂的数据库操作）
    if ($web_id > 0) {
        // 记录到日志文件
        $log_file = 'outlink_log.txt';
        $log_entry = date('Y-m-d H:i:s') . " - 网站ID: $web_id - URL: $t_url\n";
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
    
    if(!empty($t_url)) {
        preg_match('/(http|https):\/\//',$t_url,$matches);
        if($matches){
            $url=$t_url;
            $title='网站转跳中，请耐心等待...';
        } else {
            preg_match('/\./i',$t_url,$matche);
            if($matche){
                $url='http://'.$t_url;
                $title='网站转跳中，请耐心等待...';
            } else {
                $url='https://www.95dir.com';
                $title='参数错误 正在返回首页~~~';
            }
        }
    } else {
        $title='参数缺失，正在返回首页...';
        $url='https://www.95dir.com';
    }
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $title; ?></title>
    <meta http-equiv="refresh" content="3;url=<?php echo $url; ?>">
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .loading { font-size: 18px; color: #666; }
        .url { color: #0066cc; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="loading"><?php echo $title; ?></div>
    <div class="url">目标地址: <?php echo htmlspecialchars($url); ?></div>
    <div>如果没有自动跳转，请点击: <a href="<?php echo htmlspecialchars($url); ?>">这里</a></div>
    
    <script>
        // 3秒后跳转
        setTimeout(function() {
            window.location.href = '<?php echo addslashes($url); ?>';
        }, 3000);
    </script>
</body>
</html>
