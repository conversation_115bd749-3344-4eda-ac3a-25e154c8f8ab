<?php
/* Smarty version 4.5.5, created on 2025-07-22 18:49:08
  from '/www/wwwroot/www.95dir.com/themes/default/search.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_687f6ca4599628_86206022',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '25fd8389c1f3833f7f4a316d3253bac350c4dab2' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/search.html',
      1 => 1752020684,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_687f6ca4599628_86206022 (Smarty_Internal_Template $_smarty_tpl) {
?><!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/css/logo-preview.css" rel="stylesheet" type="text/css" />
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/js/logo-optimizer.js"><?php echo '</script'; ?>
>
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
            <div id="listbox" class="clearfix">
            	<h2>搜索结果<?php if ($_smarty_tpl->tpl_vars['keyword']->value) {?> - "<?php echo $_smarty_tpl->tpl_vars['keyword']->value;?>
"<?php }?> <?php if ($_smarty_tpl->tpl_vars['total']->value > 0) {?>(共找到 <?php echo $_smarty_tpl->tpl_vars['total']->value;?>
 条结果)<?php }?></h2>

            	<?php if ($_smarty_tpl->tpl_vars['search_type']->value == 'article') {?>
            	<!-- 文章搜索结果 -->
            	<ul class="sitelist">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['articles']->value, 'art', false, NULL, 'list', array (
));
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li>
                		<div class="info">
                			<h3><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></h3>
                			<p><?php echo $_smarty_tpl->tpl_vars['art']->value['art_intro'];?>
</p>
                			<address>
                				发布时间：<?php echo $_smarty_tpl->tpl_vars['art']->value['art_ctime'];?>
 |
                				分类：<a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['cate_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['cate_name'];?>
</a>
                				<?php if ($_smarty_tpl->tpl_vars['art']->value['art_tags']) {?> | 标签：
                				<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['art']->value['art_tags'], 'tag', false, NULL, 'tags', array (
  'last' => true,
  'iteration' => true,
  'total' => true,
));
$_smarty_tpl->tpl_vars['tag']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['tag']->value) {
$_smarty_tpl->tpl_vars['tag']->do_else = false;
$_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['iteration']++;
$_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last'] = $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['iteration'] === $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['total'];
?>
                					<a href="<?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['tag']->value['tag_name'];?>
</a><?php if (!(isset($_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last']) ? $_smarty_tpl->tpl_vars['__smarty_foreach_tags']->value['last'] : null)) {?>、<?php }?>
                				<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                				<?php }?>
                			</address>
                		</div>
                	</li>
                	<?php
}
if ($_smarty_tpl->tpl_vars['art']->do_else) {
?>
                	<li>没有找到相关文章！</li>
                	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</ul>
				<?php } else { ?>
				<!-- 网站搜索结果 -->
            	<ul class="sitelist">
					<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'w', false, NULL, 'list', array (
));
$_smarty_tpl->tpl_vars['w']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['w']->value) {
$_smarty_tpl->tpl_vars['w']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
" class="thumb" /></a><div class="info"><h3><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_name'];?>
</a> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_ispay'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/audit.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_istop'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/top.gif" border="0"><?php }?> <?php if ($_smarty_tpl->tpl_vars['w']->value['web_isbest'] == 1) {?><img src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/images/attr/best.gif" border="0"><?php }?></h3><p><?php echo $_smarty_tpl->tpl_vars['w']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['w']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['w']->value['web_url'];?>
</a> - <?php echo $_smarty_tpl->tpl_vars['w']->value['web_ctime'];?>
 - <a href="javascript:;" class="addfav" onClick="addfav(<?php echo $_smarty_tpl->tpl_vars['w']->value['web_id'];?>
)" title="点击收藏">收藏</a></address></div></li>
                	<?php
}
if ($_smarty_tpl->tpl_vars['w']->do_else) {
?>
                	<li>没有找到相关网站！</li>
                	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
				</ul>
				<?php }?>
            	<div class="showpage"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
            </div>
        </div>
        <div id="mainbox-right">
        	<div class="ad250x250"><?php echo get_adcode(7);?>
</div>
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,10), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                	<li><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,5,false,true), 'best');
$_smarty_tpl->tpl_vars['best']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['best']->value) {
$_smarty_tpl->tpl_vars['best']->do_else = false;
?>
                   	<li><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['best']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['best']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['best']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['best']->value['web_url'];?>
</a></address></li>
                   	<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
               	</ul>
            </div>
        </div>
    </div>
    <?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</div>
</body>
</html><?php }
}
