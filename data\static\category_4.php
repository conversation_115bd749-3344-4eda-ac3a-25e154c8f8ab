<?php
//File name: category_4.php
//Creation time: 2025-07-21 22:41:50

if (!defined('IN_HANFOX')) exit('Access Denied');

$static_data = array(
	'cate_id' => '4',
	'root_id' => '0',
	'cate_mod' => 'webdir',
	'cate_name' => '生活服务',
	'cate_dir' => 'shfw',
	'cate_url' => '',
	'cate_isbest' => '0',
	'cate_keywords' => '生活服务网站分类目录',
	'cate_description' => '生活服务网站分类目录',
	'cate_arrparentid' => '0',
	'cate_arrchildid' => '4,14,15,39,43,49,264,265,266,267,268,269,258,259,260,261,254,243,244,245,246,247,248,249,262,263,257,256,255,253,252,251,250,242,241,275,276,277,278,279,271,272,273,274,270,280,281,282,283,284',
	'cate_childcount' => '49',
	'cate_postcount' => '62',
);
?>