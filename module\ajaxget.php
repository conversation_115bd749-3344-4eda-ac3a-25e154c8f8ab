<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

require(APP_PATH.'module/webdata.php');

$type = trim($_GET['type']);
/** check site */
if ($type == 'check') {
	$url = trim($_GET['url']);
	
	if (empty($url)) {
		exit('请输入网站域名！');
	} else {
		if (!is_valid_domain($url)) {
			exit('请输入正确的网站域名！');
		}
	}
			
	$url = mysqli_real_escape_string($DB->db_link, $url);
	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM ".$DB->table('websites')." WHERE web_url='$url'");
	if ($DB->num_rows($query)) {
		$row = $DB->fetch_array($query);
		$status_text = '';
		$status_color = '';

		switch($row['web_status']) {
			case 1:
				$status_text = '该网站已被拉黑，无法提交';
				$status_color = '#333';
				break;
			case 2:
				$status_text = '该网站正在审核中，请勿重复提交';
				$status_color = '#ff6600';
				break;
			case 3:
				$status_text = '该网站已收录（' . date('Y-m-d', $row['web_ctime']) . '）';
				$status_color = '#f00';
				break;
			case 4:
				$status_text = '该网站审核不通过，请修改后重新提交';
				$status_color = '#f60';
				break;
			default:
				$status_text = '该域名已存在，请勿重复提交';
				$status_color = '#f00';
		}

		echo('<span style="color: ' . $status_color . '; font-weight: bold;">' . $status_text . '</span>');
	} else {
		echo('<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <a href="javascript: void(0);" onclick="getmeta(\''.$url.'\'); getrank(\''.$url.'\')">自动抓取&raquo;</a>');
	}
	$DB->free_result($query);
}

/** crawl */
if ($type == 'crawl') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		echo '<script type="text/javascript">';
		echo 'alert("请输入网站域名！");';
		echo '</script>';
		exit();
	}

	if (!is_valid_domain($url)) {
		echo '<script type="text/javascript">';
		echo 'alert("请输入正确的网站域名！");';
		echo '</script>';
		exit();
	}

	// 记录开始时间
	$start_time = microtime(true);

	try {
		$meta = get_sitemeta($url);

		// 检查是否成功获取到Meta信息
		if (empty($meta['title']) && empty($meta['keywords']) && empty($meta['description'])) {
			echo '<script type="text/javascript">';
			echo 'alert("无法获取网站Meta信息，请检查网站是否可以正常访问");';
			echo '</script>';
			exit();
		}

		// 转义特殊字符，防止JavaScript注入
		$title = addslashes(htmlspecialchars($meta['title'], ENT_QUOTES, 'UTF-8'));
		$keywords = addslashes(htmlspecialchars($meta['keywords'], ENT_QUOTES, 'UTF-8'));
		$description = addslashes(htmlspecialchars($meta['description'], ENT_QUOTES, 'UTF-8'));

		echo '<script type="text/javascript">';
		echo '$("#web_name").attr("value", "'.$title.'");';
		echo '$("#web_tags").attr("value", "'.$keywords.'");';
		echo '$("#web_intro").attr("value", "'.$description.'");';

		// 显示成功消息
		$end_time = microtime(true);
		$duration = round(($end_time - $start_time) * 1000, 0);
		echo 'console.log("Meta信息获取成功，耗时: '.$duration.'ms");';

		// 如果某些字段为空，给出提示
		$empty_fields = array();
		if (empty($meta['title'])) $empty_fields[] = '标题';
		if (empty($meta['keywords'])) $empty_fields[] = '关键词';
		if (empty($meta['description'])) $empty_fields[] = '描述';

		if (!empty($empty_fields)) {
			echo 'console.warn("以下字段未获取到: ' . implode(', ', $empty_fields) . '");';
		}

		echo '</script>';

	} catch (Exception $e) {
		error_log("Meta抓取异常 - URL: $url, 错误: " . $e->getMessage());
		echo '<script type="text/javascript">';
		echo 'alert("获取Meta信息时发生错误，请稍后重试");';
		echo 'console.error("Meta抓取异常: ' . addslashes($e->getMessage()) . '");';
		echo '</script>';
	}

	unset($meta);
}

/** data */
if ($type == 'data') {
	$url = trim($_GET['url']);
	if (empty($url)) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("请输入网站域名！");';
		echo '</script>';
		exit;
	} else {
		if (!is_valid_domain($url)) {
			echo '<script type="text/javascript">';
			echo '$("#data_btn").val("重新获取").prop("disabled", false);';
			echo 'alert("请输入正确的网站域名！");';
			echo '</script>';
			exit;
		}
	}

	// 清理输出缓冲区
	if (ob_get_level()) {
		ob_clean();
	}

	// 设置内容类型和禁用缓存
	header('Content-Type: text/html; charset=utf-8');
	header('Cache-Control: no-cache, must-revalidate');
	header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');

	// 立即输出开始状态
	echo '<script type="text/javascript">';
	echo 'console.log("开始获取数据: ' . $url . '");';
	echo '</script>';

	// 强制输出缓冲区
	if (ob_get_level()) {
		ob_flush();
	}
	flush();

	try {
		// 设置更长的执行时间
		set_time_limit(120);

		// 记录开始时间
		$start_total = microtime(true);

		// 逐步获取数据并实时反馈
		echo '<script type="text/javascript">';
		echo 'console.log("正在获取服务器IP...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$ip = get_serverip($url);
		$ip = $ip ? $ip : '获取失败';

		echo '<script type="text/javascript">';
		echo 'var ipField = $("#web_ip"); console.log("IP字段找到:", ipField.length);';
		echo 'ipField.val("'.$ip.'"); console.log("IP设置后的值:", ipField.val());';
		echo 'console.log("IP获取完成: ' . $ip . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取百度收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$grank = get_pagerank($url);
		$grank = is_numeric($grank) ? $grank : 0;

		echo '<script type="text/javascript">';
		echo 'var grankField = $("#web_grank"); console.log("百度收录字段找到:", grankField.length);';
		echo 'grankField.val("'.$grank.'"); console.log("百度收录设置后的值:", grankField.val());';
		echo 'console.log("百度收录量获取完成: ' . $grank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取必应收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$brank = get_baidurank($url);
		$brank = is_numeric($brank) ? $brank : 0;

		echo '<script type="text/javascript">';
		echo 'var brankField = $("#web_brank"); console.log("必应收录字段找到:", brankField.length);';
		echo 'brankField.val("'.$brank.'"); console.log("必应收录设置后的值:", brankField.val());';
		echo 'console.log("必应收录量获取完成: ' . $brank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取360收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$srank = get_sogourank($url);
		$srank = is_numeric($srank) ? $srank : 0;

		echo '<script type="text/javascript">';
		echo 'var srankField = $("#web_srank"); console.log("360收录字段找到:", srankField.length);';
		echo 'srankField.val("'.$srank.'"); console.log("360收录设置后的值:", srankField.val());';
		echo 'console.log("360收录量获取完成: ' . $srank . '");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		echo '<script type="text/javascript">';
		echo 'console.log("正在获取搜狗收录量...");';
		echo '</script>';
		if (ob_get_level()) ob_flush(); flush();

		$arank = get_alexarank($url);
		$arank = is_numeric($arank) ? $arank : 0;

		// 计算总耗时
		$total_time = round((microtime(true) - $start_total) * 1000, 2);

		echo '<script type="text/javascript">';
		echo 'var arankField = $("#web_arank"); console.log("搜狗收录字段找到:", arankField.length);';
		echo 'arankField.val("'.$arank.'"); console.log("搜狗收录设置后的值:", arankField.val());';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'console.log("搜狗收录量获取完成: ' . $arank . '");';
		echo 'console.log("数据获取完成 (总耗时: '.$total_time.'ms): IP='.$ip.', 百度='.$grank.', 必应='.$brank.', 360='.$srank.', 搜狗='.$arank.'");';
		echo 'alert("数据获取完成！\\n\\nIP: '.$ip.'\\n百度收录: '.$grank.'\\n必应收录: '.$brank.'\\n360收录: '.$srank.'\\n搜狗收录: '.$arank.'\\n\\n总耗时: '.$total_time.'ms");';
		echo '</script>';

	} catch (Exception $e) {
		echo '<script type="text/javascript">';
		echo '$("#data_btn").val("重新获取").prop("disabled", false);';
		echo 'alert("获取数据时发生错误: '.addslashes($e->getMessage()).'");';
		echo 'console.error("获取数据错误: '.addslashes($e->getMessage()).'");';
		echo '</script>';
	}
}
?>