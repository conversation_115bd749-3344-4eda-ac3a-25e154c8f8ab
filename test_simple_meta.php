<?php
// 测试简化版Meta抓取功能
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>测试简化版Meta抓取功能</h2>";

try {
    // 定义必要的常量
    define('IN_IWEBDIR', true);
    
    // 包含必要的文件
    require_once('source/include/function.php');
    require_once('source/module/webdata.php');
    
    echo "<p style='color: green;'>✓ 文件包含成功</p>";
    
    // 测试域名列表
    $test_domains = array(
        'www.baidu.com',
        'www.263.im',  // 面条网
        'github.com',
        'www.taobao.com'
    );
    
    echo "<style>
        .test-result { margin: 15px 0; padding: 15px; border: 1px solid #ddd; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .partial { background-color: #fff3cd; border-color: #ffeaa7; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
    </style>";
    
    foreach ($test_domains as $domain) {
        echo "<div class='test-result'>";
        echo "<h3>测试域名: $domain</h3>";
        
        $start_time = microtime(true);
        
        try {
            $meta = get_sitemeta($domain);
            $end_time = microtime(true);
            $duration = round(($end_time - $start_time) * 1000, 2);
            
            $has_title = !empty($meta['title']);
            $has_keywords = !empty($meta['keywords']);
            $has_description = !empty($meta['description']);
            $total_fields = ($has_title ? 1 : 0) + ($has_keywords ? 1 : 0) + ($has_description ? 1 : 0);
            
            if ($total_fields >= 2) {
                echo "<div class='success'>";
                echo "<strong>✓ 抓取成功</strong> (耗时: {$duration}ms, 获取到 {$total_fields}/3 个字段)<br>";
            } elseif ($total_fields >= 1) {
                echo "<div class='partial'>";
                echo "<strong>⚠ 部分成功</strong> (耗时: {$duration}ms, 获取到 {$total_fields}/3 个字段)<br>";
            } else {
                echo "<div class='error'>";
                echo "<strong>✗ 抓取失败</strong> (耗时: {$duration}ms, 未获取到任何字段)<br>";
            }
            
            echo "<strong>标题:</strong> " . ($has_title ? htmlspecialchars(substr($meta['title'], 0, 100)) : '(未获取到)') . "<br>";
            echo "<strong>关键词:</strong> " . ($has_keywords ? htmlspecialchars(substr($meta['keywords'], 0, 100)) : '(未获取到)') . "<br>";
            echo "<strong>描述:</strong> " . ($has_description ? htmlspecialchars(substr($meta['description'], 0, 150)) : '(未获取到)') . "<br>";
            
            echo "</div>";
            
        } catch (Exception $e) {
            $end_time = microtime(true);
            $duration = round(($end_time - $start_time) * 1000, 2);
            echo "<div class='error'>";
            echo "<strong>✗ 抓取异常</strong> (耗时: {$duration}ms)<br>";
            echo "<strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
            echo "</div>";
        }
        
        echo "</div>";
        
        // 避免请求过于频繁
        sleep(1);
    }
    
    echo "<div class='test-result'>";
    echo "<h3>修复说明</h3>";
    echo "<ul>";
    echo "<li>✓ 恢复到基于原有逻辑的简化版本</li>";
    echo "<li>✓ 保持原有的Meta标签匹配模式</li>";
    echo "<li>✓ 增加了不区分大小写的备用模式</li>";
    echo "<li>✓ 增加了OpenGraph支持</li>";
    echo "<li>✓ 添加了协议切换备用方案</li>";
    echo "<li>✓ 删除了复杂的辅助函数避免冲突</li>";
    echo "<li>✓ 保持了与原有系统的兼容性</li>";
    echo "</ul>";
    echo "</div>";
    
    // 测试面条网的具体格式
    echo "<div class='test-result'>";
    echo "<h3>面条网格式测试</h3>";
    $test_html = '
    <meta charset="utf-8" />
    <meta name="renderer" content="webkit" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>面条网-面条行业公益信息网 -  </title>
    <meta name="keywords" content="面条网，面条，信息网，帮工，求职，招聘，公益网站，面条行业，面条师傅，面机，扫皮机，和面机 ，爬杆机，面刀 ，面坊买卖，面坊转让，面条技术，面业资讯" />
    <meta name="description" content="面条网（www.263.im）永久免费的面条行业公益信息网，免费发布面坊买卖信息，免费发布帮工（面条师傅）求职招聘信息，分享面条行业最新动态，致力于打造一个权威的面条公益行业网站。 " />
    <meta name="generator" content="Discuz! X3.5" />
    ';
    
    // 模拟get_sitemeta函数的核心逻辑
    $meta_test = array('title' => '', 'keywords' => '', 'description' => '');
    
    // Title
    if (preg_match('/<title[^>]*>(.*?)<\/title>/si', $test_html, $matches)) {
        $meta_test['title'] = trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
    }
    
    // Keywords
    if (preg_match('/<meta\s+name=["\']keywords["\']\s+content=["\'](.*?)["\']/si', $test_html, $matches)) {
        $meta_test['keywords'] = trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
    }
    
    // Description
    if (preg_match('/<meta\s+name=["\']description["\']\s+content=["\'](.*?)["\']/si', $test_html, $matches)) {
        $meta_test['description'] = trim(html_entity_decode($matches[1], ENT_QUOTES, 'UTF-8'));
    }
    
    echo "<p><strong>测试结果:</strong></p>";
    echo "<p><strong>标题:</strong> " . htmlspecialchars($meta_test['title']) . "</p>";
    echo "<p><strong>关键词:</strong> " . htmlspecialchars(substr($meta_test['keywords'], 0, 100)) . "...</p>";
    echo "<p><strong>描述:</strong> " . htmlspecialchars(substr($meta_test['description'], 0, 150)) . "...</p>";
    
    if (!empty($meta_test['title']) && !empty($meta_test['keywords']) && !empty($meta_test['description'])) {
        echo "<p style='color: green;'>✓ 面条网格式完全兼容</p>";
    } else {
        echo "<p style='color: red;'>✗ 面条网格式存在问题</p>";
    }
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ 测试异常: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<hr>";
echo "<h3>系统信息:</h3>";
echo "<p>PHP版本: " . PHP_VERSION . "</p>";
echo "<p>当前时间: " . date('Y-m-d H:i:s') . "</p>";
echo "<p>CURL支持: " . (function_exists('curl_init') ? '是' : '否') . "</p>";
echo "<p>MB扩展: " . (function_exists('mb_detect_encoding') ? '是' : '否') . "</p>";
?>
