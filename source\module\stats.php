<?php
function get_stats() {
	global $DB;

	$stat = array();
	$stat['category'] = $DB->get_count($DB->table('categories'));
	// 修正：只统计已审核通过的网站（web_status=3）
	$stat['website'] = $DB->get_count($DB->table('websites'), array('web_status' => 3));
	$stat['article'] = $DB->get_count($DB->table('articles'));
	// 待审核网站（web_status=2）
	$stat['apply'] = $DB->get_count($DB->table('websites'), array('web_status' => 2));
	// VIP站点（web_ispay=1且已审核）
	$stat['vip'] = $DB->get_count($DB->table('websites'), "web_status=3 AND web_ispay=1");
	// 推荐位（web_isbest=1且已审核）
	$stat['recommend'] = $DB->get_count($DB->table('websites'), "web_status=3 AND web_isbest=1");
	// 黑名单（web_status=0）
	$stat['blacklist'] = $DB->get_count($DB->table('websites'), array('web_status' => 1));
	$stat['user'] = $DB->get_count($DB->table('users'), 'user_type' != 'admin');
	$stat['adver'] = $DB->get_count($DB->table('advers'));
	$stat['link'] = $DB->get_count($DB->table('links'));
	$stat['feedback'] = $DB->get_count($DB->table('feedbacks'));
	$stat['label'] = $DB->get_count($DB->table('labels'));
	$stat['page'] = $DB->get_count($DB->table('pages'));

	return $stat;
}
?>