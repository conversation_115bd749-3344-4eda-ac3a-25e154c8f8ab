<?php
/* Smarty version 4.5.5, created on 2025-07-15 15:28:07
  from '/www/wwwroot/www.95dir.com/themes/system/payment.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6876030772dde2_50198648',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '6485c32fda54a5f8f477b1e9a8315583be295511' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/payment.html',
      1 => 1752564481,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6876030772dde2_50198648 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<?php if ($_smarty_tpl->tpl_vars['action']->value == 'list') {?>
<h3 class="title"><em>付费订单管理</em></h3>

<!-- 统计面板 -->
<div style="padding: 10px; background: #f8f9fa; margin-bottom: 15px; border-radius: 4px;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td width="20%" style="text-align: center; padding: 10px;">
                <div style="font-size: 24px; color: #007bff; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total'];?>
</div>
                <div style="color: #666;">总订单</div>
            </td>
            <td width="20%" style="text-align: center; padding: 10px;">
                <div style="font-size: 24px; color: #ff6b6b; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['pending'];?>
</div>
                <div style="color: #666;">待支付</div>
            </td>
            <td width="20%" style="text-align: center; padding: 10px;">
                <div style="font-size: 24px; color: #51cf66; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['paid'];?>
</div>
                <div style="color: #666;">已支付</div>
            </td>
            <td width="20%" style="text-align: center; padding: 10px;">
                <div style="font-size: 24px; color: #339af0; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['completed'];?>
</div>
                <div style="color: #666;">已完成</div>
            </td>
            <td width="20%" style="text-align: center; padding: 10px;">
                <div style="font-size: 24px; color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['total_amount'];?>
</div>
                <div style="color: #666;">总收入</div>
            </td>
        </tr>
    </table>
</div>

<!-- 收入统计 -->
<div style="padding: 10px; background: #e8f5e8; margin-bottom: 15px; border-radius: 4px;">
    <strong>💰 收入统计：</strong>
    今日收入：<span style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['today_amount'];?>
</span> | 
    本月收入：<span style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['month_amount'];?>
</span> | 
    总收入：<span style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['total_amount'];?>
</span>
</div>

<!-- 筛选条件 -->
<div style="padding: 10px; background: #fff; border: 1px solid #ddd; margin-bottom: 15px;">
    <form method="get" style="margin: 0;">
        <input type="hidden" name="act" value="list">
        状态：
        <select name="status">
            <option value="">全部状态</option>
            <option value="0" <?php if ($_smarty_tpl->tpl_vars['status']->value == 0) {?>selected<?php }?>>待支付</option>
            <option value="1" <?php if ($_smarty_tpl->tpl_vars['status']->value == 1) {?>selected<?php }?>>已支付</option>
            <option value="2" <?php if ($_smarty_tpl->tpl_vars['status']->value == 2) {?>selected<?php }?>>已完成</option>
            <option value="3" <?php if ($_smarty_tpl->tpl_vars['status']->value == 3) {?>selected<?php }?>>已取消</option>
        </select>
        
        服务类型：
        <select name="payment_type">
            <option value="">全部类型</option>
            <option value="quick_review" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 'quick_review') {?>selected<?php }?>>快审</option>
            <option value="recommend" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 'recommend') {?>selected<?php }?>>推荐</option>
            <option value="vip" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 'vip') {?>selected<?php }?>>VIP服务</option>
        </select>
        
        关键词：
        <input type="text" name="keyword" value="<?php echo $_smarty_tpl->tpl_vars['keyword']->value;?>
" placeholder="订单号/网站名称/网址" style="width: 200px;">
        
        <input type="submit" value="筛选" class="btn">
        <a href="?act=config" class="btn" style="margin-left: 10px;">配置管理</a>
    </form>
</div>

<!-- 订单列表 -->
<?php if ($_smarty_tpl->tpl_vars['orders']->value) {?>
<table class="listbox" width="100%" border="0" cellspacing="1" cellpadding="0">
    <tr class="head">
        <th width="80">订单号</th>
        <th width="100">服务类型</th>
        <th width="150">网站信息</th>
        <th width="100">用户</th>
        <th width="60">金额</th>
        <th width="80">状态</th>
        <th width="120">创建时间</th>
        <th width="120">支付时间</th>
        <th width="100">操作</th>
    </tr>
    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['orders']->value, 'order');
$_smarty_tpl->tpl_vars['order']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['order']->value) {
$_smarty_tpl->tpl_vars['order']->do_else = false;
?>
    <tr>
        <td><a href="?act=view&id=<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
" title="查看详情"><?php echo $_smarty_tpl->tpl_vars['order']->value['order_no'];?>
</a></td>
        <td>
            <span style="background: <?php echo $_smarty_tpl->tpl_vars['order']->value['type_color'];?>
; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 12px;">
                <?php echo $_smarty_tpl->tpl_vars['order']->value['payment_type_name'];?>

            </span>
        </td>
        <td>
            <div style="font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['order']->value['web_name'];?>
</div>
            <div style="font-size: 12px; color: #666;"><?php echo $_smarty_tpl->tpl_vars['order']->value['web_url'];?>
</div>
        </td>
        <td><?php echo $_smarty_tpl->tpl_vars['order']->value['user_email'];?>
</td>
        <td style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['order']->value['amount'];?>
</td>
        <td>
            <span style="color: <?php echo $_smarty_tpl->tpl_vars['order']->value['status_color'];?>
; font-weight: bold;">
                <?php echo $_smarty_tpl->tpl_vars['order']->value['status_name'];?>

            </span>
        </td>
        <td><?php echo $_smarty_tpl->tpl_vars['order']->value['create_time_format'];?>
</td>
        <td><?php echo $_smarty_tpl->tpl_vars['order']->value['pay_time_format'];?>
</td>
        <td>
            <a href="?act=view&id=<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
">查看</a>
            <?php if ($_smarty_tpl->tpl_vars['order']->value['status'] == 0) {?>
            | <a href="javascript:updateStatus(<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
, 1)">标记已付</a>
            <?php }?>
            <?php if ($_smarty_tpl->tpl_vars['order']->value['status'] == 1) {?>
            | <a href="javascript:updateStatus(<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
, 2)">标记完成</a>
            <?php }?>
            | <a href="?act=delete&id=<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
" onclick="return confirm('确定删除此订单吗？')">删除</a>
        </td>
    </tr>
    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
</table>

<div style="padding: 10px;">
    <?php echo $_smarty_tpl->tpl_vars['pagenav']->value;?>

</div>
<?php } else { ?>
<div style="padding: 20px; text-align: center; color: #666;">
    暂无订单数据
</div>
<?php }?>



<?php } elseif ($_smarty_tpl->tpl_vars['action']->value == 'view') {?>
<h3 class="title"><em>订单详情</em></h3>

<div style="padding: 15px;">
    <table class="form" width="100%" border="0" cellspacing="1" cellpadding="0">
        <tr>
            <th width="120">订单号：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['order_no'];?>
</td>
        </tr>
        <tr>
            <th>服务类型：</th>
            <td>
                <?php if ($_smarty_tpl->tpl_vars['order']->value['payment_type'] == 'quick_review') {?>
                <span style="background: #ffc107; color: #000; padding: 4px 8px; border-radius: 4px;">快审</span>
                <?php } elseif ($_smarty_tpl->tpl_vars['order']->value['payment_type'] == 'recommend') {?>
                <span style="background: #28a745; color: #fff; padding: 4px 8px; border-radius: 4px;">推荐</span>
                <?php } elseif ($_smarty_tpl->tpl_vars['order']->value['payment_type'] == 'vip') {?>
                <span style="background: #dc3545; color: #fff; padding: 4px 8px; border-radius: 4px;">VIP服务</span>
                <?php }?>
            </td>
        </tr>
        <tr>
            <th>网站名称：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['web_name'];?>
</td>
        </tr>
        <tr>
            <th>网站地址：</th>
            <td><a href="<?php echo $_smarty_tpl->tpl_vars['order']->value['web_url'];?>
" target="_blank"><?php echo $_smarty_tpl->tpl_vars['order']->value['web_url'];?>
</a></td>
        </tr>
        <tr>
            <th>网站简介：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['web_intro'];?>
</td>
        </tr>
        <tr>
            <th>提交用户：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['user_email'];?>
 (<?php echo $_smarty_tpl->tpl_vars['order']->value['user_nickname'];?>
)</td>
        </tr>
        <tr>
            <th>支付金额：</th>
            <td style="color: #28a745; font-weight: bold; font-size: 16px;">¥<?php echo $_smarty_tpl->tpl_vars['order']->value['amount'];?>
</td>
        </tr>
        <tr>
            <th>订单状态：</th>
            <td>
                <?php if ($_smarty_tpl->tpl_vars['order']->value['status'] == 0) {?>
                <span style="color: #ff6b6b; font-weight: bold;">待支付</span>
                <?php } elseif ($_smarty_tpl->tpl_vars['order']->value['status'] == 1) {?>
                <span style="color: #51cf66; font-weight: bold;">已支付</span>
                <?php } elseif ($_smarty_tpl->tpl_vars['order']->value['status'] == 2) {?>
                <span style="color: #339af0; font-weight: bold;">已完成</span>
                <?php } elseif ($_smarty_tpl->tpl_vars['order']->value['status'] == 3) {?>
                <span style="color: #868e96; font-weight: bold;">已取消</span>
                <?php }?>
            </td>
        </tr>
        <tr>
            <th>创建时间：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['create_time_format'];?>
</td>
        </tr>
        <tr>
            <th>支付时间：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['order']->value['pay_time_format'];?>
</td>
        </tr>
        <?php if ($_smarty_tpl->tpl_vars['website']->value) {?>
        <tr>
            <th>关联网站：</th>
            <td>
                <a href="website.php?act=edit&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" target="_blank">
                    <?php echo $_smarty_tpl->tpl_vars['website']->value['web_name'];?>
 (ID: <?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
)
                </a>
            </td>
        </tr>
        <?php }?>
    </table>
    
    <div style="margin-top: 20px;">
        <a href="?act=list" class="btn">返回列表</a>
        <?php if ($_smarty_tpl->tpl_vars['order']->value['status'] == 0) {?>
        <a href="javascript:updateStatus(<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
, 1)" class="btn">标记已支付</a>
        <?php }?>
        <?php if ($_smarty_tpl->tpl_vars['order']->value['status'] == 1) {?>
        <a href="javascript:updateStatus(<?php echo $_smarty_tpl->tpl_vars['order']->value['order_id'];?>
, 2)" class="btn">标记已完成</a>
        <?php }?>
    </div>
</div>

<?php } elseif ($_smarty_tpl->tpl_vars['action']->value == 'config') {?>
<h3 class="title"><em>💳 支付配置管理</em></h3>

<div style="padding: 20px; background: #fff; border: 1px solid #ddd; border-radius: 4px;">
    <form method="post" action="?act=config">
        <input type="hidden" name="do" value="update">
        <input type="hidden" name="config_id" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['config_id'];?>
">
        <input type="hidden" name="status" value="1">
        <table width="100%" border="0" cellspacing="0" cellpadding="8">
            <tr>
                <td width="120" style="background: #f8f9fa; font-weight: bold;">商户ID：</td>
                <td>
                    <input type="text" name="merchant_id" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['merchant_id'];?>
" style="width: 200px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">易支付商户ID</span>
                </td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">商户密钥：</td>
                <td>
                    <input type="text" name="merchant_key" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['merchant_key'];?>
" style="width: 400px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">用于签名验证</span>
                </td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">API地址：</td>
                <td>
                    <input type="text" name="api_url" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['api_url'];?>
" style="width: 300px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">易支付接口地址</span>
                </td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">通讯密钥：</td>
                <td>
                    <input type="text" name="api_key" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['api_key'];?>
" style="width: 400px; padding: 5px;">
                    <span style="color: #666; margin-left: 10px;">软件通讯密钥（可选）</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="background: #e3f2fd; padding: 10px; font-weight: bold; color: #1976d2;">💰 服务价格设置</td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">快速审核：</td>
                <td>
                    ¥<input type="number" name="quick_review_price" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['quick_review_price'];?>
" step="0.01" min="0" style="width: 100px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">24小时内审核 + 快速通道处理</span>
                </td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">审核推荐：</td>
                <td>
                    ¥<input type="number" name="recommend_price" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['recommend_price'];?>
" step="0.01" min="0" style="width: 100px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">优先审核处理 + 推荐位展示</span>
                </td>
            </tr>
            <tr>
                <td style="background: #f8f9fa; font-weight: bold;">VIP服务：</td>
                <td>
                    ¥<input type="number" name="vip_price" value="<?php echo $_smarty_tpl->tpl_vars['config']->value['vip_price'];?>
" step="0.01" min="0" style="width: 100px; padding: 5px;" required>
                    <span style="color: #666; margin-left: 10px;">即时审核通过 + VIP标识 + 首页推荐</span>
                </td>
            </tr>
            <tr>
                <td colspan="2" style="padding: 20px; text-align: center;">
                    <input type="submit" value="保存配置" style="background: #007bff; color: white; padding: 10px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                    <input type="button" value="测试支付" onclick="window.open('../simple_jump_test.php', '_blank')" style="background: #28a745; color: white; padding: 10px 30px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; margin-left: 10px;">
                </td>
            </tr>
        </table>
    </form>
</div>

<div style="margin-top: 20px; padding: 15px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px;">
    <h4 style="margin-top: 0; color: #856404;">📋 配置说明</h4>
    <ul style="color: #856404; margin-bottom: 0;">
        <li><strong>商户ID</strong>：您在易支付平台的商户标识</li>
        <li><strong>商户密钥</strong>：用于支付签名验证，请妥善保管</li>
        <li><strong>API地址</strong>：易支付接口地址，通常为 https://pay.xxx.com/</li>
        <li><strong>通讯密钥</strong>：软件通讯密钥，用于高级功能（可选）</li>
        <li><strong>服务价格</strong>：根据您的业务需求设置不同服务的价格</li>
    </ul>
</div>

<?php } elseif ($_smarty_tpl->tpl_vars['action']->value == 'statistics') {?>
<h3 class="title"><em>📊 收入统计分析</em></h3>

<!-- 基础统计 -->
<div style="padding: 15px; background: #f8f9fa; margin-bottom: 20px; border-radius: 4px;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #007bff; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total_orders'];?>
</div>
                <div style="color: #666; margin-top: 5px;">总订单数</div>
            </td>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #28a745; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['paid_orders'];?>
</div>
                <div style="color: #666; margin-top: 5px;">已支付订单</div>
            </td>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #dc3545; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['pending_orders'];?>
</div>
                <div style="color: #666; margin-top: 5px;">待支付订单</div>
            </td>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['total_amount'];?>
</div>
                <div style="color: #666; margin-top: 5px;">总收入</div>
            </td>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #ff6b6b; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['today_amount'];?>
</div>
                <div style="color: #666; margin-top: 5px;">今日收入</div>
            </td>
            <td width="16.66%" style="text-align: center; padding: 15px;">
                <div style="font-size: 28px; color: #339af0; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['month_amount'];?>
</div>
                <div style="color: #666; margin-top: 5px;">本月收入</div>
            </td>
        </tr>
    </table>
</div>

<!-- 收入对比 -->
<div style="padding: 15px; background: #fff; border: 1px solid #ddd; margin-bottom: 20px; border-radius: 4px;">
    <h4 style="margin-top: 0; color: #333;">💰 收入对比</h4>
    <table width="100%" border="0" cellspacing="0" cellpadding="8">
        <tr style="background: #f8f9fa;">
            <td width="25%" style="font-weight: bold;">时间段</td>
            <td width="25%" style="font-weight: bold;">收入金额</td>
            <td width="25%" style="font-weight: bold;">时间段</td>
            <td width="25%" style="font-weight: bold;">收入金额</td>
        </tr>
        <tr>
            <td>今日收入</td>
            <td style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['today_amount'];?>
</td>
            <td>昨日收入</td>
            <td style="color: #6c757d; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['yesterday_amount'];?>
</td>
        </tr>
        <tr>
            <td>本周收入</td>
            <td style="color: #007bff; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['week_amount'];?>
</td>
            <td>本月收入</td>
            <td style="color: #339af0; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stats']->value['month_amount'];?>
</td>
        </tr>
    </table>
</div>

<!-- 服务类型统计 -->
<?php if ($_smarty_tpl->tpl_vars['type_stats']->value) {?>
<div style="padding: 15px; background: #fff; border: 1px solid #ddd; margin-bottom: 20px; border-radius: 4px;">
    <h4 style="margin-top: 0; color: #333;">🎯 服务类型统计</h4>
    <table width="100%" border="0" cellspacing="1" cellpadding="8" style="background: #f0f0f0;">
        <tr style="background: #f8f9fa;">
            <td style="font-weight: bold;">服务类型</td>
            <td style="font-weight: bold;">订单数量</td>
            <td style="font-weight: bold;">收入金额</td>
            <td style="font-weight: bold;">占比</td>
        </tr>
        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['type_stats']->value, 'stat');
$_smarty_tpl->tpl_vars['stat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['stat']->value) {
$_smarty_tpl->tpl_vars['stat']->do_else = false;
?>
        <tr style="background: #fff;">
            <td><?php echo $_smarty_tpl->tpl_vars['stat']->value['type_name'];?>
</td>
            <td><?php echo $_smarty_tpl->tpl_vars['stat']->value['count'];?>
</td>
            <td style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stat']->value['amount'];?>
</td>
            <td><?php echo sprintf("%.1f",($_smarty_tpl->tpl_vars['stat']->value['amount']/$_smarty_tpl->tpl_vars['stats']->value['total_amount']*100));?>
%</td>
        </tr>
        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    </table>
</div>
<?php }?>

<!-- 支付方式统计 -->
<?php if ($_smarty_tpl->tpl_vars['pay_stats']->value) {?>
<div style="padding: 15px; background: #fff; border: 1px solid #ddd; margin-bottom: 20px; border-radius: 4px;">
    <h4 style="margin-top: 0; color: #333;">💳 支付方式统计</h4>
    <table width="100%" border="0" cellspacing="1" cellpadding="8" style="background: #f0f0f0;">
        <tr style="background: #f8f9fa;">
            <td style="font-weight: bold;">支付方式</td>
            <td style="font-weight: bold;">订单数量</td>
            <td style="font-weight: bold;">收入金额</td>
            <td style="font-weight: bold;">占比</td>
        </tr>
        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['pay_stats']->value, 'stat');
$_smarty_tpl->tpl_vars['stat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['stat']->value) {
$_smarty_tpl->tpl_vars['stat']->do_else = false;
?>
        <tr style="background: #fff;">
            <td><?php echo $_smarty_tpl->tpl_vars['stat']->value['type_name'];?>
</td>
            <td><?php echo $_smarty_tpl->tpl_vars['stat']->value['count'];?>
</td>
            <td style="color: #28a745; font-weight: bold;">¥<?php echo $_smarty_tpl->tpl_vars['stat']->value['amount'];?>
</td>
            <td><?php echo sprintf("%.1f",($_smarty_tpl->tpl_vars['stat']->value['amount']/$_smarty_tpl->tpl_vars['stats']->value['total_amount']*100));?>
%</td>
        </tr>
        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    </table>
</div>
<?php }?>

<?php }?>

<?php echo '<script'; ?>
>
function updateStatus(orderId, status) {
    if (!confirm('确定要更新订单状态吗？')) {
        return;
    }
    
    $.post('?act=update_status', {
        order_id: orderId,
        status: status
    }, function(data) {
        if (data.success) {
            alert('状态更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    }, 'json');
}
<?php echo '</script'; ?>
>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
