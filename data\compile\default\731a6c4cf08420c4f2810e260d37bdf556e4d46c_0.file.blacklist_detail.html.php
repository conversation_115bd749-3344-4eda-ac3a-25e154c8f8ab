<?php
/* Smarty version 4.5.5, created on 2025-07-22 11:02:54
  from '/www/wwwroot/www.95dir.com/themes/default/blacklist_detail.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_687eff5ee07ab2_71216350',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '731a6c4cf08420c4f2810e260d37bdf556e4d46c' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/default/blacklist_detail.html',
      1 => 1752398509,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:script.html' => 1,
    'file:topbar.html' => 1,
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_687eff5ee07ab2_71216350 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/www/wwwroot/www.95dir.com/source/extend/smarty/plugins/modifier.date_format.php','function'=>'smarty_modifier_date_format',),));
?>
<!DOCTYPE HTML>
<html>
<head>
<title><?php echo $_smarty_tpl->tpl_vars['site_title']->value;?>
</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="<?php echo $_smarty_tpl->tpl_vars['site_keywords']->value;?>
" />
<meta name="Description" content="<?php echo $_smarty_tpl->tpl_vars['site_description']->value;?>
" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/css/logo-preview.css" rel="stylesheet" type="text/css" />
<?php $_smarty_tpl->_subTemplateRender("file:script.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
themes/default/skin/svg-fix.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 type="text/javascript" src="<?php echo $_smarty_tpl->tpl_vars['site_root']->value;?>
public/js/logo-optimizer.js"><?php echo '</script'; ?>
>

</head>

<body>
<?php $_smarty_tpl->_subTemplateRender("file:topbar.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
<div id="wrapper">
	<?php $_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- 黑名单状态横幅 -->
    		<div style="background: linear-gradient(45deg, #dc3545, #c82333); color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
    			<div style="position: absolute; top: -10px; right: -10px; width: 80px; height: 80px; opacity: 0.3; font-size: 60px; transform: rotate(15deg);">⚠️</div>
    			<h2 style="margin: 0; font-size: 18px; font-weight: bold;">🛡️ 黑名单网站 - <?php echo $_smarty_tpl->tpl_vars['web']->value['category_name'];?>
</h2>
    			<p style="margin: 5px 0 0 0; font-size: 14px;">该网站已被列入黑名单，存在安全风险或违规内容</p>
    			<p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.9;">拉黑时间：<?php echo $_smarty_tpl->tpl_vars['web']->value['blacklist_time_formatted'];?>
 | 操作员：<?php echo $_smarty_tpl->tpl_vars['web']->value['web_blacklist_operator'];?>
</p>
    		</div>
        	<div id="siteinfo">
            	<h1 class="wtitle">
                    <span style="float: right; margin-top: 5px;">
                        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#dc3545;border-radius:1em;padding:5px 15px;">
                            <?php echo $_smarty_tpl->tpl_vars['web']->value['category_name'];?>

                        </span>
                    </span>
                    <em style="color: #dc3545;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
 - 黑名单网站</em>
                </h1>

				<ul class="wdata">
                    <li class="line"><em style="color: #dc3545;">黑名单</em>网站状态</li>
                    <li class="line"><em style="color: #dc3545;"><?php echo $_smarty_tpl->tpl_vars['web']->value['category_name'];?>
</em>违规分类</li>
                    <li class="line"><em style="color: #666;">已隐藏</em>访问地址</li>
                    <li class="line"><em style="color: #666;">已屏蔽</em>搜索收录</li>
                    <li class="line"><em style="color: #666;">已禁用</em>访问统计</li>
                    <li class="line"><em style="color: #666;">已停止</em>推荐展示</li>
                    <li class="line"><em style="color: #666;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_blacklist_operator'];?>
</em>操作员</li>
                    <li class="line"><em style="color: #666;">-</em>安全等级</li>
                    <li class="line"><em><?php echo $_smarty_tpl->tpl_vars['web']->value['blacklist_time_formatted'];?>
</em>拉黑时间</li>
                    <li class="line"><em><?php echo $_smarty_tpl->tpl_vars['web']->value['ctime_formatted'];?>
</em>提交时间</li>
                    <li class="line"><em><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ctime'];?>
</em>提交日期</li>
                    <li class="line"><em style="color: #f60;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_utime'];?>
</em>更新日期</li>
                </ul>

                <div class="blank10"></div>

                <div class="clearfix params" style="display: flex; align-items: flex-start; gap: 15px;">
                    <div style="position: relative; flex-shrink: 0;">
                        <img src="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_pic'];?>
" width="130" height="110" alt="<?php echo $_smarty_tpl->tpl_vars['web']->value['web_name'];?>
" class="wthumb" />
                        <div style="position: absolute; top: 5px; right: 5px; width: 60px; height: 20px; background: #dc3545; color: white; text-align: center; font-size: 12px; line-height: 20px; border-radius: 3px; opacity: 0.9; z-index: 10;">黑名单</div>
                    </div>
                    <ul class="siteitem" style="flex: 1; margin: 0; padding: 0; list-style: none;">
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;">
                        <strong>网站地址：</strong>
                        <span style="color:#f00;font-size:12px;">隐私保护：网址信息已隐藏</span>
                        <!-- 黑名单状态标识 -->
                        <span style="line-height:20px;color:#FFF;font-size:12px;text-align:center;background:#dc3545;border-radius:1em;float:right;width:60px;margin-left:10px;">
                            黑名单
                        </span>
                    </li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>域名信息：</strong><span style="color: #666;">
                        <?php echo '<script'; ?>
>
                        document.write(function(){
                            var domain = '<?php echo $_smarty_tpl->tpl_vars['web']->value['domain'];?>
';
                            if(domain.length > 8) {
                                return domain.substring(0, 3) + '****' + domain.substring(domain.length-3);
                            } else if(domain.length > 4) {
                                return domain.substring(0, 2) + '****' + domain.substring(domain.length-2);
                            } else {
                                return '****';
                            }
                        }());
                        <?php echo '</script'; ?>
>
                        <noscript>****</noscript>
                    </span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>服务器IP：</strong><span style="color: #666;">隐私保护</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>违规分类：</strong><span style="color: #dc3545; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['web']->value['category_name'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>拉黑理由：</strong><span style="color: #856404; line-height: 23px;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_blacklist_reason'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>拉黑时间：</strong><span style="color: #666;"><?php echo $_smarty_tpl->tpl_vars['web']->value['blacklist_time_formatted'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>操作员：</strong><span style="color: #666;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_blacklist_operator'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>网站描述：</strong><span style="line-height: 23px;"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_intro'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>TAG标签：</strong><?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['web_tags']->value, 'item');
$_smarty_tpl->tpl_vars['item']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['item']->value) {
$_smarty_tpl->tpl_vars['item']->do_else = false;
?><a href="javascript:void(0)" style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 5px; text-decoration: none;"><?php echo $_smarty_tpl->tpl_vars['item']->value['tag_name'];?>
</a><?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><?php echo get_adcode(1);?>
</li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>网站状态：</strong><span style="color: #dc3545; font-weight: bold;">该网站已被列入黑名单，存在安全风险</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>提交时间：</strong><span style="color: #666;"><?php echo $_smarty_tpl->tpl_vars['web']->value['ctime_formatted'];?>
</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0;"><strong>本页地址：</strong><a href="<?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=blacklist_detail&id=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
" style="color: #007bff; text-decoration: none;"><?php echo $_smarty_tpl->tpl_vars['site_url']->value;?>
?mod=blacklist_detail&id=<?php echo $_smarty_tpl->tpl_vars['web']->value['web_id'];?>
</a></li>
                    </ul>
                </div>

                <div class="blank10"></div>

                <!-- 拉黑理由 -->
                <div class="params">
                    <h3 style="color: #dc3545;">⚠️ 拉黑理由</h3>
                    <div class="content" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; color: #856404;">
                        <?php echo $_smarty_tpl->tpl_vars['web']->value['web_blacklist_reason'];?>

                    </div>
                </div>

                <div class="blank10"></div>

                <!-- 网站介绍 -->
                <div class="params">
                    <h3>网站介绍</h3>
                    <div class="content">
                        <?php if ($_smarty_tpl->tpl_vars['web']->value['web_ai_intro']) {?>
                            <div class="web_ai_intro"><?php echo $_smarty_tpl->tpl_vars['web']->value['web_ai_intro'];?>
</div>
                            <div class="blank10"></div>
                        <?php }?>
                        <?php echo $_smarty_tpl->tpl_vars['web']->value['web_intro'];?>

                    </div>
                </div>

               <div class="blank10"></div>

                <!-- 同分类黑名单网站 -->
                 <!--<div class="params">
                    <h3>同分类黑名单</h3>
                    <div class="content">
                        <?php if ($_smarty_tpl->tpl_vars['related_blacklist']->value) {?>
                        <ul class="rellist" style="list-style: none; padding: 0;">
                            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['related_blacklist']->value, 'rel');
$_smarty_tpl->tpl_vars['rel']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['rel']->value) {
$_smarty_tpl->tpl_vars['rel']->do_else = false;
?>
                            <li style="margin-bottom: 10px; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;">
                                <a href="?mod=blacklist_detail&id=<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_id'];?>
" style="display: flex; align-items: center; text-decoration: none; color: #333;">
                                    <img src="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_pic'];?>
" width="60" height="48" alt="<?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
" style="margin-right: 10px; border-radius: 3px;" />
                                    <div>
                                        <strong style="color: #dc3545;"><?php echo $_smarty_tpl->tpl_vars['rel']->value['web_name'];?>
</strong>
                                        <div style="font-size: 12px; color: #666; margin-top: 2px;">违规分类：<?php echo $_smarty_tpl->tpl_vars['rel']->value['category_name'];?>
</div>
                                    </div>
                                </a>
                            </li>
                            <?php
}
if ($_smarty_tpl->tpl_vars['rel']->do_else) {
?>
                            <li style="text-align: center; color: #666; padding: 20px;">暂无其他同分类黑名单网站</li>
                            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                        </ul>
                        <?php } else { ?>
                        <p style="text-align: center; color: #666; padding: 20px;">暂无其他同分类黑名单网站</p>
                        <?php }?>
                    </div>
                </div>

                <div class="blank10"></div>-->
            <!-- 黑名单警告说明 -->
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-left: 4px solid #dc3545; border-radius: 5px; padding: 12px 15px; margin-bottom: 15px; color: #721c24; font-size: 13px;">
                <strong>⚠️ 安全警告：</strong>该网站已被列入黑名单，可能存在安全风险或违规内容。为保护用户安全，已隐藏具体网址和访问入口。请谨慎访问此类网站，建议选择其他优质网站。
            </div>

            </div>

            <div class="blank10"></div>

            <!-- 上一站下一站导航 -->
            <div class="website-navigation" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="prev-website" style="flex: 1; text-align: left;">
                        <?php if ($_smarty_tpl->tpl_vars['prev_website']->value) {?>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['prev_website']->value['web_link'];?>
" style="color: #dc3545; text-decoration: none; font-size: 14px;"
                               onmouseover="this.style.color='#c82333'" onmouseout="this.style.color='#dc3545'">
                                <i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
                                上一站：<?php echo $_smarty_tpl->tpl_vars['prev_website']->value['web_name'];?>

                            </a>
                        <?php } else { ?>
                            <span style="color: #6c757d; font-size: 14px;">
                                <i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
                                上一站：暂无
                            </span>
                        <?php }?>
                    </div>

                    <div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
                        <span style="color: #6c757d; font-size: 12px;">黑名单导航</span>
                    </div>

                    <div class="next-website" style="flex: 1; text-align: right;">
                        <?php if ($_smarty_tpl->tpl_vars['next_website']->value) {?>
                            <a href="<?php echo $_smarty_tpl->tpl_vars['next_website']->value['web_link'];?>
" style="color: #dc3545; text-decoration: none; font-size: 14px;"
                               onmouseover="this.style.color='#c82333'" onmouseout="this.style.color='#dc3545'">
                                下一站：<?php echo $_smarty_tpl->tpl_vars['next_website']->value['web_name'];?>

                                <i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
                            </a>
                        <?php } else { ?>
                            <span style="color: #6c757d; font-size: 14px;">
                                下一站：暂无
                                <i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
                            </span>
                        <?php }?>
                    </div>
                </div>
            </div>

        </div>

        <div id="mainbox-right">

            <!-- VIP站点推荐 -->
            <div id="bestweb" class="mag">
                <h3>vip站点</h3>
                <ul class="weblist_b">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,'views'), 'quick');
$_smarty_tpl->tpl_vars['quick']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['quick']->value) {
$_smarty_tpl->tpl_vars['quick']->do_else = false;
?>
                    <li><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
"><img src="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_pic'];?>
" width="100" height="80" alt="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
" /></a><strong><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_name'];?>
</a></strong><p><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_intro'];?>
</p><address><a href="<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_furl'];?>
" target="_blank" class="visit" onClick="clickout(<?php echo $_smarty_tpl->tpl_vars['quick']->value['web_id'];?>
)"><?php echo $_smarty_tpl->tpl_vars['quick']->value['web_url'];?>
</a></address></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 推荐资讯 -->
            <div id="bestart">
                <h3>推荐资讯</h3>
                <ul class="artlist_b">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_articles(0,10), 'art');
$_smarty_tpl->tpl_vars['art']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['art']->value) {
$_smarty_tpl->tpl_vars['art']->do_else = false;
?>
                    <li>[<em><a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['cate_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['art']->value['cate_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['cate_name'];?>
</a></em>]<a href="<?php echo $_smarty_tpl->tpl_vars['art']->value['art_link'];?>
"><?php echo $_smarty_tpl->tpl_vars['art']->value['art_title'];?>
</a></li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 最新收录 -->
            <div id="bestart">
                <h3>最新收录 <span style="float: right; font-size: 12px; color: #999; font-weight: normal;"><?php echo smarty_modifier_date_format(time(),"%Y-%m-%d");?>
</span></h3>
                <ul class="artlist_b">
                    <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, get_websites(0,10,'ctime'), 'new');
$_smarty_tpl->tpl_vars['new']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['new']->value) {
$_smarty_tpl->tpl_vars['new']->do_else = false;
?>
                    <li>
                        <a href="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_link'];?>
" title="<?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
"><?php echo $_smarty_tpl->tpl_vars['new']->value['web_name'];?>
</a>
                        <span style="float: right; font-size: 11px; color: #999;"><?php echo smarty_modifier_date_format($_smarty_tpl->tpl_vars['new']->value['web_ctime'],"%m-%d");?>
</span>
                    </li>
                    <?php
}
if ($_smarty_tpl->tpl_vars['new']->do_else) {
?>
                    <li style="color: #999; text-align: center;">暂无收录网站</li>
                    <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 右侧广告位 -->
            <div class="ad250x250">
                <?php echo get_adcode(2);?>

            </div>
        </div>
    </div>
</div>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>
</body>
</html>
<?php }
}
