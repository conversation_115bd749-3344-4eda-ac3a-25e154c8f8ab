{#include file="header.html"#}

            <div class="content">
            	<div class="title">{#$pagename#}</div>
                <div class="body">
            		{#if $action == 'list'#}
                	<div id="listbox">
						<table width="100%" border="0" cellspacing="1" cellpadding="0">
							<tr>
								<th>ID</th>
								<th>所属分类</th>
								<th>网站名称</th>
								<th>网站地址</th>
								<th>属性状态</th>
								<th>提交时间</th>
								<th>操作选项</th>
							</tr>
							{#foreach from=$websites item=web#}
							<tr>
								<td>{#$web.web_id#}</td>
								<td>{#$web.cate_name#}</td>
								<td class="textleft">{#$web.web_name#}</td>
								<td class="textleft">{#$web.web_url#}</td>
								<td style="color: #FF0000;">{#$web.web_status#}</td>
								<td>{#$web.web_ctime#}</td>
								<td><a href="?mod=website&act=edit&wid={#$web.web_id#}">修改</a></td>
							</tr>
							{#foreachelse#}
							<tr><td colspan="7">您还未提交任何站点！</td></tr>
							{#/foreach#}
						</table>
					</div>
        			<div id="showpage" class="clearfix">{#$showpage#}</div>
        			{#/if#}
        
        			{#if $action == 'add' || $action == 'edit'#}
        			{#if $cfg.is_enabled_submit == 'yes'#}

<h3>
	<strong>收录流程说明</strong> 
</h3>
<ul>
	<li>
		<span><strong>审核方式</strong></span><span>：人工审核与程序辅助审核结合。</span> 
	</li>
	<li>
		<span><strong>收录时效</strong></span><span>：提交申请后，审核结果通常在 </span><span><strong>7个工作日以内</strong></span><span> 反馈。</span> 
	</li>
	<li>
		<span><strong>加速推荐</strong></span><span>：打赏赞助本站 </span><span><strong>10元以上</strong></span><span> 并备注您的域名，可申请成为“推荐网站”，享受更多展示流量。</span> 
	</li>
</ul>
<hr />
<h3>
	<strong>网站收录标准</strong> 
</h3>
<span>ꔷ </span><span><strong>可访问性</strong></span><span>：网站需正常开放，无访问限制（如无需注册或付费访问）。</span> 
<p>
	<br />
</p>
<p>
	<span> ꔷ </span><span><strong>内容质量</strong></span><span>：需提供完整、有价值的内容，正在建设中或内容空泛的网站暂不收录。</span> 
</p>
<p>
	<span> ꔷ </span><span><strong>内容合规</strong></span><span>：禁止含色情、反动、赌博等违法违规信息。</span> 
</p>
<p>
	<span> ꔷ </span><span><strong>访问速度</strong></span><span>：服务器响应时间不超过 </span><span><strong>3秒</strong></span><span>，确保用户体验流畅。</span> 
</p>
<hr />
<h3>
	<strong>优先收录条件</strong> 
</h3>
<span>ꔷ 为本站添加友情链接：</span> 
<pre><a href="https://www.95dir.com">95分类目录</a></pre>
<span>ꔷ 网站已完成 </span><span><strong>ICP备案</strong></span><span> 及 </span><span><strong>公安备案</strong></span><span>（国内站点建议优先提交）。</span> 
<p>
	<br />
</p>
<p>
	<span> ꔷ 网站已被主流搜索引擎（如Google、Bing、百度等）收录。</span> 
</p>
<hr />
<span><strong>提交申请前，请确保符合上述标准。</strong></span> 
<p>
	<br />
</p>
<p>
	<span><strong>感谢您对95DIR的支持，共建优质网络生态！</strong></span> 
</p>
<hr />
<span><strong>注</strong></span><span>：<span style="color:#E53333;">若需加急审核或推荐展示，可通过赞助备注域名申请，详情联系站长。</span></span> 
        	
 <div class="blank10"></div>       	
       	
        			
<div id="formbox">
    <div><h1>提交您的你的网站</h1><p>与95dir.com分享您的网站，获得有价值的反向链接，让成千上万的用户发现您</p><a href="https://frogdr.com/95dir.com?utm_source=95dir.com" target="_blank"><img src="https://frogdr.com/95dir.com/badge-white.svg" alt="Monitor&#0032;your&#0032;Domain&#0032;Rating&#0032;with&#0032;FrogDR" width="250" height="54"></a></div>
    
    <form name="myfrom" id="myfrom" method="post" action="?mod=website">
        <ul>
            <li><strong>选择分类：</strong><select name="cate_id">{#$category_option#}</select></li>
            <li><strong>网站名称：</strong><input type="text" name="web_name" id="web_name" class="ipt" size="50" maxlength="12" value="{#$web.web_name#}" onblur="checkWebName(this.value)" onkeyup="checkWebNameLength(this)" /><span id="web_name_msg" style="color: #999; font-size: 12px; margin-left: 10px;">最多12个字符（6个汉字）</span></li>
            <li><strong>网站域名：</strong><input type="text" name="web_url" id="web_url" class="ipt" size="50" maxlength="100" placeholder="输入或粘贴URL" value="{#$web.web_url#}" onblur="checkurl(this.value)" /><input type="button" class="btn" id="meta_btn" value="抓取Meta" onclick="getmeta()"><br><span id="url_msg" style="color: #999; font-size: 12px; margin-left: 10px;">输入域名后自动检测是否已收录</span></li>
            <li><strong>TAG标签：</strong><input type="text" name="web_tags" id="web_tags" class="ipt" size="50" maxlength="50" value="{#$web.web_tags#}" onBlur="javascript:this.value=this.value.replace(/，/ig,',');" /></li>
            <li><strong>网站简介：</strong><textarea name="web_intro" id="web_intro" cols="50" rows="6" class="ipt">{#$web.web_intro#}</textarea></li>
            <li><strong>服务器IP：</strong><input name="web_ip" type="text" class="ipt" id="web_ip" size="30" maxlength="30" value="{#$web.web_ip#}" readonly /><input type="button" class="btn" id="data_btn" value="获取数据" onclick="getdata()"></li>
            <li><strong>PageRank：</strong><input name="web_grank" type="text" class="ipt" id="web_grank" size="10" maxlength="2" value="{#(!$web.web_grank) ? '0' : $web.web_grank#}" readonly /></li>
            <li><strong>BaiduRank：</strong><input name="web_brank" type="text" class="ipt" id="web_brank" size="10" maxlength="2" value="{#(!$web.web_brank) ? '0' : $web.web_brank#}" readonly /></li>
            <li><strong>SogouRank：</strong><input name="web_srank" type="text" class="ipt" id="web_srank" size="10" maxlength="2" value="{#(!$web.web_srank) ? '0' : $web.web_srank#}" readonly /></li>
            <li><strong>AlexaRank：</strong><input name="web_arank" type="text" class="ipt" id="web_arank" size="10" maxlength="10" value="{#(!$web.web_arank) ? '0' : $web.web_arank#}" readonly /></li>
            <li><strong>&nbsp;</strong><input type="hidden" name="web_id" id="web_id" value="{#$web.web_id#}"><input type="hidden" name="do" id="do" value="{#$do#}"><input type="submit" class="btn" value="提 交"> <input type="reset" class="btn" value="重 填"></li>
        </ul>
    </form>
</div>

<style>
    .uzkoo {
        margin: 1rem 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(45deg, #fd8434, #ffa994, #fd3c3c, #ff9e88, #ff6060, #ff146e, #ff96df);
        background-size: 400% 400%;
        animation: gradientAnimation 10s ease infinite;
        border-radius: 0.6rem;
        padding: 1rem 0;
    }

    @keyframes gradientAnimation {
        0% {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }

        100% {
            background-position: 0% 50%;
        }
    }
    .uzkoo a {
        color: #fff;
        font-size: 3rem;
        font-weight: bold;
        text-decoration: none;
    }
</style>
<div class="uzkoo">
    <a target="_blank" href="https://www.uzkoo.com/">全新的分类目录站点，点击去提交</a>
</div>



        			{#else#}
        			<div style="background: #ffc; border: dashed 1px #f30; color: #f00; padding: 20px; text-align: center;">{#$cfg.submit_close_reason#}</div>
        			{#/if#}
					{#/if#}
                </div>
            </div>


            <script>
        // DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const urlInput = document.getElementById('web_url');
            
            // 实时输入处理
            urlInput.addEventListener('input', function(e) {
                this.value = sanitizeURL(this.value);
            });

            // 失焦时最终校验
            urlInput.addEventListener('blur', function(e) {
                this.value = finalCheckURL(this.value);
            });
        });

        // 即时清理函数
        function sanitizeURL(url) {
            return url
                .replace(/^\s+/, '')         // 去除首部空格
                .replace(/^(https?|ftp):\/\//i,'')  // 移除协议头
                .replace(/^\/+/g, '')        // 移除开头的斜杠
                .replace(/\s+/g, '')         // 移除所有空格
                .replace(/\/+/g, '/');       // 合并多余斜杠
        }

        // 最终校验函数
        function finalCheckURL(url) {
            const cleaned = sanitizeURL(url)
                .replace(/\/+$/g, '')        // 去除末尾斜杠
                .trim();

            // 添加自定义校验逻辑（示例）
            if (!cleaned) {
                console.warn('URL不能为空');
            }

            return cleaned;
        }

        // 网站名称长度检查函数
        function checkWebNameLength(input) {
            const value = input.value;
            const msgElement = document.getElementById('web_name_msg');

            // 计算字符长度（中文算2个字符）
            let length = 0;
            for (let i = 0; i < value.length; i++) {
                if (value.charCodeAt(i) > 127) {
                    length += 2; // 中文字符算2个字符
                } else {
                    length += 1; // 英文字符算1个字符
                }
            }

            if (length > 12) {
                msgElement.innerHTML = '<span style="color: #f00;">网站名称过长！最多12个字符（6个汉字）</span>';
                msgElement.style.color = '#f00';
                return false;
            } else if (length === 0) {
                msgElement.innerHTML = '最多12个字符（6个汉字）';
                msgElement.style.color = '#999';
            } else {
                msgElement.innerHTML = `已输入${length}/12个字符`;
                msgElement.style.color = '#666';
            }
            return true;
        }

        // 网站名称失焦验证
        function checkWebName(name) {
            const msgElement = document.getElementById('web_name_msg');

            if (!name.trim()) {
                msgElement.innerHTML = '<span style="color: #f00;">请输入网站名称！</span>';
                return false;
            }

            // 计算字符长度
            let length = 0;
            for (let i = 0; i < name.length; i++) {
                if (name.charCodeAt(i) > 127) {
                    length += 2;
                } else {
                    length += 1;
                }
            }

            if (length > 12) {
                msgElement.innerHTML = '<span style="color: #f00;">网站名称过长！最多12个字符（6个汉字）</span>';
                return false;
            }

            msgElement.innerHTML = `已输入${length}/12个字符`;
            msgElement.style.color = '#666';
            return true;
        }

        // URL检测函数
        function checkurl(url) {
            const msgElement = document.getElementById('url_msg');

            if (url == '') {
                msgElement.innerHTML = '<span style="color: #f00;">请输入网站域名！</span>';
                return false;
            }

            // 清理URL格式
            url = sanitizeURL(url);

            msgElement.innerHTML = '<img src="{#$site_url#}public/images/loading.gif" width="16" height="16" style="vertical-align: middle;"> 正在验证，请稍候...';

            // 使用jQuery发送AJAX请求
            if (typeof $ !== 'undefined') {
                $.ajax({
                    type: "GET",
                    url: '{#$site_url#}?mod=ajaxget&type=check',
                    data: 'url=' + url,
                    cache: false,
                    timeout: 10000,
                    success: function(data) {
                        console.log('URL检测成功:', data);
                        msgElement.innerHTML = data;
                    },
                    error: function(xhr, status, error) {
                        console.error('URL检测失败:', xhr.status, status, error);
                        msgElement.innerHTML = '<span style="color: #f00;">检测失败，请稍后重试</span>';
                    }
                });
            } else {
                msgElement.innerHTML = '<span style="color: #f00;">系统错误：jQuery未加载</span>';
            }
            return true;
        }

        // 抓取网站Meta信息（完全恢复原有逻辑）
        function getmeta() {
            var url = $("#web_url").attr("value");
            if (url == '') {
                alert('请输入网站域名！');
                $("#web_url").focus();
                return false;
            }
            $(document).ready(function(){
                $("#meta_btn").val('正在获取，请稍候...');
                $.ajax({
                    type: "GET",
                    url: sitepath + '?mod=ajaxget&type=crawl',
                    data: 'url=' + url,
                    datatype: "script",
                    cache: false,
                    success: function(data){
                        $("body").append(data);
                        $("#meta_btn").val('重新获取');
                    }
                });
            });
        }

        // 获取网站数据（修复版本）
        function getdata() {
            var url = $("#web_url").attr("value");
            if (url == '') {
                alert('请输入网站域名！');
                $("#web_url").focus();
                return false;
            }

            // 显示加载状态
            $("#data_btn").val('正在获取，请稍候...').prop('disabled', true);

            $.ajax({
                type: "GET",
                url: sitepath + '?mod=ajaxget&type=data',
                data: 'url=' + encodeURIComponent(url),
                dataType: "html",
                cache: false,
                timeout: 120000, // 2分钟超时
                success: function(data) {
                    console.log('获取数据成功:', data);
                    $("body").append(data);
                    // 注意：按钮状态由服务器端的JavaScript代码控制
                },
                error: function(xhr, status, error) {
                    console.error('获取数据失败:', status, error);
                    alert('获取数据失败，请稍后重试。错误信息：' + error);
                    $("#data_btn").val('重新获取').prop('disabled', false);
                }
            });
        }

        // 表单提交验证
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('myfrom');
            if (form) {
                form.addEventListener('submit', function(e) {
                    const webNameInput = document.getElementById('web_name');
                    if (!checkWebName(webNameInput.value)) {
                        e.preventDefault();
                        alert('网站名称不符合要求，请检查后重新提交！');
                        webNameInput.focus();
                        return false;
                    }
                });
            }
        });
    </script>
{#include file="footer.html"#}