<?php
// Meta格式测试页面
define('IN_IWEBDIR', true);
require_once('source/module/webdata.php');

// 测试HTML内容 - 包含您提供的Meta格式
$test_html = '
<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8" />
<meta name="renderer" content="webkit" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>面条网-面条行业公益信息网 -  </title>

<meta name="keywords" content="面条网，面条，信息网，帮工，求职，招聘，公益网站，面条行业，面条师傅，面机，扫皮机，和面机 ，爬杆机，面刀 ，面坊买卖，面坊转让，面条技术，面业资讯" />
<meta name="description" content="面条网（www.263.im）永久免费的面条行业公益信息网，免费发布面坊买卖信息，免费发布帮工（面条师傅）求职招聘信息，分享面条行业最新动态，致力于打造一个权威的面条公益行业网站。 " />
<meta name="generator" content="Discuz! X3.5" />
</head>
<body>
<h1>面条网首页</h1>
<p>这是一个测试页面，用于验证Meta信息提取功能。</p>
</body>
</html>
';

// 其他格式的测试HTML
$test_formats = array(
    'standard' => $test_html,
    
    'no_quotes' => '
    <html><head>
    <title>无引号格式测试</title>
    <meta name=keywords content="测试,关键词,无引号">
    <meta name=description content="这是无引号格式的描述测试">
    </head></html>
    ',
    
    'mixed_quotes' => '
    <html><head>
    <title>混合引号格式测试</title>
    <meta name="keywords" content=\'单引号关键词,测试\'>
    <meta name=\'description\' content="双引号描述测试">
    </head></html>
    ',
    
    'property_format' => '
    <html><head>
    <title>Property格式测试</title>
    <meta property="keywords" content="property格式,关键词">
    <meta property="og:description" content="OpenGraph描述测试">
    </head></html>
    ',
    
    'reverse_order' => '
    <html><head>
    <title>反序格式测试</title>
    <meta content="反序关键词,测试" name="keywords">
    <meta content="反序描述测试" name="description">
    </head></html>
    ',
    
    'broken_html' => '
    <html><head>
    <title>损坏HTML测试
    <meta name="keywords" content="损坏HTML,关键词
    <meta name="description" content="损坏HTML描述测试，没有结束引号
    </head></html>
    '
);

echo "<h2>Meta格式提取测试</h2>";
echo "<style>
    .test-case { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
    .success { background-color: #d4edda; border-color: #c3e6cb; }
    .partial { background-color: #fff3cd; border-color: #ffeaa7; }
    .error { background-color: #f8d7da; border-color: #f5c6cb; }
    .html-preview { background-color: #f8f9fa; padding: 10px; margin: 10px 0; font-family: monospace; font-size: 12px; }
</style>";

foreach ($test_formats as $format_name => $html_content) {
    echo "<div class='test-case'>";
    echo "<h3>测试格式: " . ucfirst($format_name) . "</h3>";
    
    // 显示HTML内容预览
    echo "<div class='html-preview'>";
    echo "<strong>HTML内容预览:</strong><br>";
    echo htmlspecialchars(trim($html_content));
    echo "</div>";
    
    $start_time = microtime(true);
    
    try {
        // 测试标准提取
        $meta_standard = extract_meta_from_content($html_content);
        
        // 测试宽松模式提取
        $meta_loose = extract_meta_loose_mode($html_content);
        
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        
        // 判断提取结果
        $standard_success = !empty($meta_standard['title']) || !empty($meta_standard['keywords']) || !empty($meta_standard['description']);
        $loose_success = !empty($meta_loose['title']) || !empty($meta_loose['keywords']) || !empty($meta_loose['description']);
        
        if ($standard_success) {
            echo "<div class='success'>";
            echo "<strong>✓ 标准模式提取成功</strong> (耗时: {$duration}ms)<br>";
        } elseif ($loose_success) {
            echo "<div class='partial'>";
            echo "<strong>⚠ 宽松模式提取成功</strong> (耗时: {$duration}ms)<br>";
        } else {
            echo "<div class='error'>";
            echo "<strong>✗ 提取失败</strong> (耗时: {$duration}ms)<br>";
        }
        
        echo "<strong>标准模式结果:</strong><br>";
        echo "标题: " . htmlspecialchars($meta_standard['title']) . "<br>";
        echo "关键词: " . htmlspecialchars($meta_standard['keywords']) . "<br>";
        echo "描述: " . htmlspecialchars($meta_standard['description']) . "<br><br>";
        
        echo "<strong>宽松模式结果:</strong><br>";
        echo "标题: " . htmlspecialchars($meta_loose['title']) . "<br>";
        echo "关键词: " . htmlspecialchars($meta_loose['keywords']) . "<br>";
        echo "描述: " . htmlspecialchars($meta_loose['description']) . "<br>";
        
        echo "</div>";
        
    } catch (Exception $e) {
        $end_time = microtime(true);
        $duration = round(($end_time - $start_time) * 1000, 2);
        echo "<div class='error'>";
        echo "<strong>✗ 提取异常</strong> (耗时: {$duration}ms)<br>";
        echo "<strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "<br>";
        echo "</div>";
    }
    
    echo "</div>";
}

echo "<div class='test-case'>";
echo "<h3>功能增强说明</h3>";
echo "<ul>";
echo "<li>✓ 支持标准双引号格式: name=\"keywords\" content=\"...\"</li>";
echo "<li>✓ 支持无引号格式: name=keywords content=\"...\"</li>";
echo "<li>✓ 支持混合引号格式: name='keywords' content=\"...\"</li>";
echo "<li>✓ 支持Property格式: property=\"og:description\"</li>";
echo "<li>✓ 支持反序格式: content=\"...\" name=\"keywords\"</li>";
echo "<li>✓ 支持损坏HTML的宽松模式提取</li>";
echo "<li>✓ 支持多种备用Meta标签名称</li>";
echo "<li>✓ 智能内容长度和质量检测</li>";
echo "</ul>";
echo "</div>";
?>
