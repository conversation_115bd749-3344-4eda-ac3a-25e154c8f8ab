<?php
    // 调试版本的go.php
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    echo "<h3>Go.php 调试信息</h3>";
    
    $t_url = isset($_GET['url']) ? $_GET['url'] : '';
    $web_id = isset($_GET['id']) ? intval($_GET['id']) : 0;
    
    echo "<p>URL参数: " . htmlspecialchars($t_url) . "</p>";
    echo "<p>网站ID: $web_id</p>";
    
    // 记录今日出站点击统计
    if ($web_id > 0) {
        try {
            echo "<p>开始初始化系统...</p>";
            
            // 按照系统标准方式初始化
            define('IN_IWEBDIR', true);
            define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
            define('APP_PATH', ROOT_PATH.'source/');
            
            echo "<p>ROOT_PATH: " . ROOT_PATH . "</p>";
            echo "<p>APP_PATH: " . APP_PATH . "</p>";
            
            // 检查关键文件是否存在
            if (file_exists(APP_PATH.'init.php')) {
                echo "<p>✓ init.php 文件存在</p>";
                require(APP_PATH.'init.php');
                echo "<p>✓ init.php 加载成功</p>";
            } else {
                echo "<p>✗ init.php 文件不存在</p>";
            }
            
            // 检查数据库连接
            if (isset($DB) && is_object($DB)) {
                echo "<p>✓ 数据库连接成功</p>";
                
                // 测试数据库操作
                $webdata_table = $DB->table('webdata');
                echo "<p>webdata表名: $webdata_table</p>";
                
                $spider_table = $DB->table('spider_stats');
                echo "<p>spider_stats表名: $spider_table</p>";
                
                // 尝试查询
                $today = date('Y-m-d');
                $today_record = $DB->fetch_one("SELECT id FROM $spider_table WHERE stat_date = '$today'");
                
                if ($today_record) {
                    echo "<p>✓ 找到今日记录，ID: " . $today_record['id'] . "</p>";
                    $DB->query("UPDATE $spider_table SET total_outlinks = total_outlinks + 1 WHERE stat_date = '$today'");
                    echo "<p>✓ 更新今日出站次数成功</p>";
                } else {
                    echo "<p>今日记录不存在，创建新记录...</p>";
                    $DB->query("INSERT INTO $spider_table (stat_date, total_outlinks) VALUES ('$today', 1)");
                    echo "<p>✓ 创建今日记录成功</p>";
                }
                
                // 更新webdata表
                $DB->query("UPDATE $webdata_table SET web_outstat=web_outstat+1 WHERE web_id='$web_id' LIMIT 1");
                echo "<p>✓ 更新webdata表成功</p>";
                
            } else {
                echo "<p>✗ 数据库连接失败</p>";
                if (isset($DB)) {
                    echo "<p>DB变量存在但不是对象: " . gettype($DB) . "</p>";
                } else {
                    echo "<p>DB变量不存在</p>";
                }
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>";
            echo "<p>错误文件: " . $e->getFile() . "</p>";
            echo "<p>错误行号: " . $e->getLine() . "</p>";
        }
    } else {
        echo "<p>网站ID无效，跳过统计</p>";
    }
    
    // 处理URL跳转逻辑
    if(!empty($t_url)) {
        preg_match('/(http|https):\/\//',$t_url,$matches);
        if($matches){
            $url=$t_url;
            $title='网站转跳中，请耐心等待...';
        } else {
            preg_match('/\./i',$t_url,$matche);
            if($matche){
                $url='http://'.$t_url;
                $title='网站转跳中，请耐心等待...';
            } else {
                $url='https://www.95dir.com';
                $title='参数错误 正在返回首页~~~';
            }
        }
    } else {
        $title='参数缺失，正在返回首页...';
        $url='https://www.95dir.com';
    }
    
    echo "<p>最终跳转URL: " . htmlspecialchars($url) . "</p>";
    echo "<p>标题: $title</p>";
    
    echo "<hr>";
    echo "<p><a href='" . htmlspecialchars($url) . "' target='_blank'>点击这里手动跳转</a></p>";
    echo "<p><a href='test_outlink.php'>返回测试页面</a></p>";
?>
