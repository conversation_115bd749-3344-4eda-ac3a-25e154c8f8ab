# Meta抓取功能加强优化总结

## 问题背景

用户反馈有域名获取不到Meta信息，需要检查和加强优化获取逻辑。通过分析用户提供的Meta标签格式：

```html
<meta charset="utf-8" />
<meta name="renderer" content="webkit" />
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<title>面条网-面条行业公益信息网 -  </title>
<meta name="keywords" content="面条网，面条，信息网，帮工，求职，招聘，公益网站，面条行业，面条师傅，面机，扫皮机，和面机 ，爬杆机，面刀 ，面坊买卖，面坊转让，面条技术，面业资讯" />
<meta name="description" content="面条网（www.263.im）永久免费的面条行业公益信息网，免费发布面坊买卖信息，免费发布帮工（面条师傅）求职招聘信息，分享面条行业最新动态，致力于打造一个权威的面条公益行业网站。 " />
<meta name="generator" content="Discuz! X3.5" />
```

## 加强优化方案

### 1. 多策略抓取机制

#### 策略1: 增强版Meta抓取
- 使用 `get_url_content_for_meta_enhanced()` 函数
- 支持多种User-Agent轮换
- 分层抓取策略（Range请求 → 完整请求）
- 智能编码检测和转换

#### 策略2: 协议切换
- HTTPS失败时自动尝试HTTP
- 适应不同网站的协议支持情况

#### 策略3: 域名格式尝试
- 自动添加www前缀
- 自动去除www前缀
- 适应不同的域名配置

#### 策略4: 原有函数备用
- 使用原有的 `get_url_content()` 函数作为备用
- 保持向后兼容性

#### 策略5: file_get_contents备用
- 使用PHP内置函数作为简单备用方案
- 适用于简单的HTTP请求

#### 策略6: 源码查看备用方案
- 新增 `get_meta_from_source_view()` 函数
- 尝试多种源码获取方式
- 专门针对Meta信息优化

#### 策略7: 宽松模式提取
- 新增 `extract_meta_loose_mode()` 函数
- 处理损坏或特殊格式的HTML
- 更灵活的正则表达式匹配

### 2. 增强的Meta标签识别

#### Title提取增强
```php
$title_patterns = array(
    '/<title[^>]*>(.*?)<\/title>/si',
    '/<title[^>]*>(.*?)$/si', // 没有结束标签
    '/title[^>]*content[^>]*["\']([^"\']+)["\']>/si', // Meta格式
    '/<h1[^>]*>(.*?)<\/h1>/si', // 备用H1标签
);
```

#### Keywords提取增强
```php
$keywords_patterns = array(
    // 标准格式
    '/<meta\s+name=["\']keywords["\']\s+content=["\'](.*?)["\']/si',
    // 无引号格式
    '/<meta\s+name=keywords\s+content=["\'](.*?)["\']/si',
    // 反序格式
    '/<meta\s+content=["\'](.*?)["\']\s+name=["\']keywords["\']/si',
    // Property格式
    '/<meta\s+property=["\']keywords["\']\s+content=["\'](.*?)["\']/si',
    // 其他变体
    '/<meta\s+name=["\']news_keywords["\']\s+content=["\'](.*?)["\']/si',
    '/<meta\s+property=["\']article:tag["\']\s+content=["\'](.*?)["\']/si'
);
```

#### Description提取增强
```php
$description_patterns = array(
    // 标准格式
    '/<meta\s+name=["\']description["\']\s+content=["\'](.*?)["\']/si',
    // 无引号格式
    '/<meta\s+name=description\s+content=["\'](.*?)["\']/si',
    // OpenGraph格式
    '/<meta\s+property=["\']og:description["\']\s+content=["\'](.*?)["\']/si',
    // Twitter格式
    '/<meta\s+property=["\']twitter:description["\']\s+content=["\'](.*?)["\']/si',
    // 其他变体
    '/<meta\s+name=["\']summary["\']\s+content=["\'](.*?)["\']/si',
    '/<meta\s+name=["\']abstract["\']\s+content=["\'](.*?)["\']/si'
);
```

### 3. 宽松模式特性

#### 处理损坏HTML
- 支持没有结束标签的情况
- 支持没有引号的属性值
- 支持混合引号格式

#### 智能内容提取
- 从页面段落中提取描述
- 长度和质量检测
- 过滤版权等无用信息

#### 灵活的正则匹配
- 更宽松的空白字符处理
- 支持中文Meta标签名称
- 容错性更强的匹配模式

### 4. 技术改进

#### 编码处理增强
```php
$detected_encoding = mb_detect_encoding($data, array('UTF-8', 'GB2312', 'GBK', 'BIG5', 'ISO-8859-1'), true);
if ($detected_encoding && $detected_encoding != 'UTF-8') {
    $converted_data = @mb_convert_encoding($data, 'UTF-8', $detected_encoding);
}
```

#### 内容类型检测
```php
if ($content_type && !preg_match('/text\/html|application\/xhtml/i', $content_type)) {
    // 跳过非HTML内容
}
```

#### 智能User-Agent轮换
```php
$user_agents = array(
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15...',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101...',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36...'
);
```

## 测试文件

### 1. `test_meta_formats.php`
- 测试各种Meta标签格式
- 验证标准模式和宽松模式
- 包含您提供的面条网格式测试

### 2. `test_meta.php` (更新)
- 添加更多测试域名
- 包含面条网等实际网站测试
- 性能和成功率统计

## 预期效果

### 提高成功率
- 支持更多Meta标签格式
- 处理各种边缘情况
- 多重备用策略保障

### 增强兼容性
- 支持不同CMS系统（如Discuz）
- 支持各种编码格式
- 适应不同服务器配置

### 改善用户体验
- 更准确的Meta信息提取
- 更快的响应时间
- 更好的错误处理

## 部署建议

1. **测试验证**: 先使用测试文件验证功能
2. **逐步部署**: 可以先在测试环境部署
3. **监控日志**: 关注error_log中的抓取日志
4. **性能监控**: 观察抓取耗时和成功率
5. **用户反馈**: 收集用户使用反馈进行进一步优化
