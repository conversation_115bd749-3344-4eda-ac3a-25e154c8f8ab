<?php
// 简单的音乐播放器测试页面
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/article.php');

echo "<h2>音乐播放器测试</h2>";

// 测试音乐链接提取功能
echo "<h3>测试音乐链接提取：</h3>";

// 测试文本 - 只包含MP3链接
$test_content = '
这是一篇包含MP3音乐链接的文章：
<audio controls><source src="https://example.com/music/song1.mp3" type="audio/mpeg"></audio>
MP3外链1：https://example.com/music/song1.mp3
MP3外链2：https://demo.com/audio/song2.mp3?v=123
MP3外链3：https://cdn.music.com/files/song3.mp3
非MP3链接（应该被忽略）：https://y.qq.com/n/yqq/song/003a1tne1nSz1Y.html
非MP3链接（应该被忽略）：https://demo.com/audio/test.wav
';

$extracted_urls = extract_music_urls($test_content);
echo "<p>从测试文本中提取到的音乐链接：</p>";
echo "<ul>";
foreach ($extracted_urls as $url) {
    echo "<li>$url</li>";
}
echo "</ul>";

// 测试获取音乐列表
echo "<h3>测试获取音乐列表：</h3>";
$music_links = get_music_links(286, 10);
echo "<p>从数据库获取到 " . count($music_links) . " 个音乐链接：</p>";
echo "<ul>";
foreach ($music_links as $link) {
    echo "<li><strong>{$link['title']}</strong>: {$link['url']}</li>";
}
echo "</ul>";

// 测试AJAX接口
echo "<h3>测试AJAX接口：</h3>";
echo '<button onclick="testMusicAPI()">测试音乐API</button>';
echo '<div id="api-result"></div>';

echo '<script>
function testMusicAPI() {
    fetch("?mod=ajaxget&type=music_list")
        .then(response => response.json())
        .then(data => {
            document.getElementById("api-result").innerHTML = "<pre>" + JSON.stringify(data, null, 2) + "</pre>";
        })
        .catch(error => {
            document.getElementById("api-result").innerHTML = "<p style=\"color:red;\">错误: " + error + "</p>";
        });
}
</script>';

echo "<h3>音乐播放器演示：</h3>";
echo '<div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <h4>简单音乐播放器</h4>
    <audio controls style="width: 100%;">
        <source src="https://music.163.com/song/media/outer/url?id=347230" type="audio/mpeg">
        您的浏览器不支持音频播放。
    </audio>
    <p>这是一个简单的HTML5音频播放器示例。</p>
</div>';
?>
