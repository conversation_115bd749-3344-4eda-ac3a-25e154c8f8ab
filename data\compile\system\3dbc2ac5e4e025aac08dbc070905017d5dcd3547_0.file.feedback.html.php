<?php
/* Smarty version 4.5.5, created on 2025-07-21 09:34:51
  from '/www/wwwroot/www.95dir.com/themes/system/feedback.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_687d993b426af7_21182924',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '3dbc2ac5e4e025aac08dbc070905017d5dcd3547' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/feedback.html',
      1 => 1739588647,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_687d993b426af7_21182924 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

    <?php if ($_smarty_tpl->tpl_vars['action']->value == 'list') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em></h3>
    <div class="listbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
        <div class="search">
			<input name="keywords" type="text" id="keywords" class="ipt" size="30" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" />
			<input type="submit" class="btn" value="搜索" />
        </div>
        </form>
                   
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<div class="toolbar">
			<select name="act" id="act" class="sel">
			<option value="del" style="color: #FF0000;">删除选定</option>
			</select>
			<input type="submit" class="btn" value="应用" onClick="if(IsCheck('fb_id[]')==false){alert('请指定您要操作的意见ID！');return false;}else{return confirm('确认执行此操作吗？');}">
		</div>
                        
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th><input type="checkbox" id="ChkAll" onClick="CheckAll(this.form)"></th>
				<th>ID</th>
				<th>用户昵称</th>
				<th>电子邮件</th>
				<th>提交时间</th>
				<th>操作选项</th>
			</tr>
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['feedback']->value, 'fb');
$_smarty_tpl->tpl_vars['fb']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['fb']->value) {
$_smarty_tpl->tpl_vars['fb']->do_else = false;
?>
			<tr>
				<td><input name="fb_id[]" type="checkbox" value="<?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_id'];?>
"></td>
				<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_id'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_nick'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_email'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_date'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_operate'];?>
</td>
			</tr>
			<?php
}
if ($_smarty_tpl->tpl_vars['fb']->do_else) {
?>
			<tr><td colspan="6">无任何反馈信息！</td></tr>
			<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
		</table>
		</form>
        <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
	</div>
    <?php }?>

    <?php if ($_smarty_tpl->tpl_vars['action']->value == 'view') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em></h3>
    <div class="formbox">
		<form name="mform" method="post" action="">
        <table width="100%" border="0" cellspacing="1" cellpadding="0">
        	<tr>
            	<th>用户昵称：</th>
                <td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_nick'];?>
</td>
            </tr>
            <tr>
            	<th>电子邮件：</th>
                <td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_email'];?>
</td>
            </tr>
           	<tr>
            	<th>反馈内容：</th>
            	<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_content'];?>
</td>
            </tr>
            <tr>
            	<th>提交时间：</th>
            	<td><?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_date'];?>
</td>
            </tr>
            <tr class="btnbox">
            	<th>&nbsp;</th>
            	<td>
                	<input type="button" class="btn" value="删 除" onclick="if (confirm('确认删除此内容吗？')) { window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
&act=del&fb_id=<?php echo $_smarty_tpl->tpl_vars['fb']->value['fb_id'];?>
'}">&nbsp;
                	<input type="button" class="btn" value="返回列表" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">
                </td>
            </tr>
         </table>
         </form>
	</div>
    <?php }?>              

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
