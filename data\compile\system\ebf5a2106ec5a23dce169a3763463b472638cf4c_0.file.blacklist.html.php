<?php
/* Smarty version 4.5.5, created on 2025-07-13 07:23:33
  from '/www/wwwroot/www.95dir.com/themes/system/blacklist.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_6872ee75b664f6_95316716',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'ebf5a2106ec5a23dce169a3763463b472638cf4c' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/blacklist.html',
      1 => 1752238938,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_6872ee75b664f6_95316716 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<h3 class="title"><em>黑名单管理</em><span>共 <?php echo $_smarty_tpl->tpl_vars['total']->value;?>
 个黑名单网站</span></h3>

<div class="listbox">
    <!-- 分类筛选 -->
    <div class="toolbar">
        <select class="sel" onChange="if(this.options[this.selectedIndex].value!=''){location='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=list&category='+this.options[this.selectedIndex].value;}">
            <option value="">所有分类</option>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['categories']->value, 'cat_name', false, 'cat_id');
$_smarty_tpl->tpl_vars['cat_name']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cat_id']->value => $_smarty_tpl->tpl_vars['cat_name']->value) {
$_smarty_tpl->tpl_vars['cat_name']->do_else = false;
?>
            <option value="<?php echo $_smarty_tpl->tpl_vars['cat_id']->value;?>
"<?php if ($_smarty_tpl->tpl_vars['current_category']->value == $_smarty_tpl->tpl_vars['cat_id']->value) {?> selected<?php }?>><?php echo $_smarty_tpl->tpl_vars['cat_name']->value;?>
</option>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </select>
        <span style="margin-left: 20px;">
            <a href="website.php?status=1" class="btn">网站管理</a>
        </span>
    </div>

    <table width="100%" border="0" cellspacing="1" cellpadding="0">
        <tr>
            <th width="60">ID</th>
            <th width="200">网站名称</th>
            <th width="250">网站地址</th>
            <th width="100">黑名单分类</th>
            <th width="300">拉黑理由</th>
            <th width="100">操作员</th>
            <th width="120">拉黑时间</th>
            <th width="100">操作</th>
        </tr>
        <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['websites']->value, 'website');
$_smarty_tpl->tpl_vars['website']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['website']->value) {
$_smarty_tpl->tpl_vars['website']->do_else = false;
?>
        <tr>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
</td>
            <td class="ltext">
                <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=detail&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" title="查看详情">
                    <?php echo $_smarty_tpl->tpl_vars['website']->value['web_name'];?>

                </a>
            </td>
            <td class="ltext">
                <a href="http://<?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>
" target="_blank" title="<?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>
">
                    <?php if (strlen($_smarty_tpl->tpl_vars['website']->value['web_url']) > 30) {?>
                        <?php echo substr((string) $_smarty_tpl->tpl_vars['website']->value['web_url'], (int) 0, (int) 30);?>
...
                    <?php } else { ?>
                        <?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>

                    <?php }?>
                </a>
            </td>
            <td>
                <span style="color: #f00;"><?php echo $_smarty_tpl->tpl_vars['website']->value['category_name'];?>
</span>
            </td>
            <td class="ltext" title="<?php echo $_smarty_tpl->tpl_vars['website']->value['web_blacklist_reason'];?>
">
                <?php echo $_smarty_tpl->tpl_vars['website']->value['reason_short'];?>

            </td>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['web_blacklist_operator'];?>
</td>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['blacklist_time_formatted'];?>
</td>
            <td>
                <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=detail&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
">详情</a>
                |
                <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=restore&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" 
                   onclick="return confirm('确认恢复此网站为待审核状态吗？');" 
                   style="color: #080;">恢复</a>
            </td>
        </tr>
        <?php
}
if ($_smarty_tpl->tpl_vars['website']->do_else) {
?>
        <tr><td colspan="8">暂无黑名单网站</td></tr>
        <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
    </table>
    
    <div class="pagebox"><?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>
</div>
</div>

<style>
.pagination {
    text-align: center;
    margin: 20px 0;
}

.pagination a, .pagination strong {
    display: inline-block;
    padding: 5px 10px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
    color: #333;
}

.pagination a:hover {
    background-color: #f5f5f5;
}

.pagination strong {
    background-color: #007cba;
    color: white;
    border-color: #007cba;
}

.ltext {
    text-align: left !important;
    padding-left: 10px !important;
}

.toolbar {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 3px;
}

.toolbar .sel {
    margin-right: 10px;
}

.btn {
    background: #007cba;
    color: white;
    padding: 5px 15px;
    text-decoration: none;
    border-radius: 3px;
    border: none;
    cursor: pointer;
}

.btn:hover {
    background: #005a87;
}
</style>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
