//搜索
$(document).ready(function(){
    $("#selopt").hover(
        function(){
            $("#options").slideDown();
            $("#options li a").click(function(){
                $("#cursel").text($(this).text());
                $("#type").attr("value", $(this).attr("name"));
                $("#options").hide();
            });
        },
        
        function(){$("#options").hide();}
    )   
})

//搜索伪静态
function rewrite_search(){
	var type = $("#type").val();
	var query = $.trim($("#query").val());
	if (type == null) {type = "tags"}
	if (query == "") {
		alert("\u8bf7\u8f93\u5165\u641c\u7d22\u5173\u952e\u5b57\uff01");
		$("#query").focus();
		return false;
	} else {
		if (rewrite == 1) {
			window.location.href = sitepath + "search-" + type + "-" + encodeURI(query) + ".html";
		} else if (rewrite == 2) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query) + ".html";
		} else if (rewrite == 3) {
			window.location.href = sitepath + "search/" + type + "/" + encodeURI(query);
		} else {
			this.form.submit();
		}
	}
	return false;
}

//验证url
function checkurl(url){
	if (url == '') {
		$("#msg").html('请输入网站域名！');
		return false;
	}
	
	$(document).ready(function(){$("#msg").html('<img src="' + sitepath + 'public/images/loading.gif" align="absmiddle"> 正在验证，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=check', data: 'url=' + url, cache: false, success: function(data){$("#msg").html(data)}});});
return true;
};

//获取META - 优化版本
function getmeta() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}

	// 防止重复点击
	if ($("#meta_btn").val() == '正在获取，请稍候...') {
		return false;
	}

	$(document).ready(function(){
		$("#meta_btn").val('正在获取，请稍候...').prop('disabled', true);

		$.ajax({
			type: "GET",
			url: sitepath + '?mod=ajaxget&type=crawl',
			data: 'url=' + url,
			datatype: "script",
			cache: false,
			timeout: 30000, // 30秒超时
			success: function(data){
				try {
					$("body").append(data);
					$("#meta_btn").val('重新获取').prop('disabled', false);
				} catch(e) {
					console.error('处理返回数据时出错:', e);
					alert('处理返回数据时出错，请重试');
					$("#meta_btn").val('重新获取').prop('disabled', false);
				}
			},
			error: function(xhr, status, error) {
				$("#meta_btn").val('重新获取').prop('disabled', false);

				if (status === 'timeout') {
					alert('获取超时，请检查网站是否可以正常访问，或稍后重试');
				} else if (status === 'error') {
					if (xhr.status === 0) {
						alert('网络连接失败，请检查网络连接');
					} else {
						alert('获取失败 (错误代码: ' + xhr.status + ')，请稍后重试');
					}
				} else {
					alert('获取失败，请稍后重试');
				}
				console.error('Meta获取失败:', status, error, xhr);
			}
		});
	});
}

//获取IP, PageRank, Sogou PageRank, Alexa
function getdata() {
	var url = $("#web_url").attr("value");
	if (url == '') {
		alert('请输入网站域名！');
		$("#web_url").focus();
		return false;
	}

	// 显示加载状态
	$("#data_btn").val('正在获取，请稍候...').prop('disabled', true);

	$.ajax({
		type: "GET",
		url: sitepath + '?mod=ajaxget&type=data',
		data: 'url=' + encodeURIComponent(url),
		dataType: "html",
		cache: false,
		timeout: 120000, // 2分钟超时
		success: function(data) {
			console.log('获取数据成功:', data);
			$("body").append(data);
			// 注意：按钮状态由服务器端的JavaScript代码控制
		},
		error: function(xhr, status, error) {
			console.error('获取数据失败:', status, error);
			alert('获取数据失败，请稍后重试。错误信息：' + error);
			$("#data_btn").val('重新获取').prop('disabled', false);
		}
	});
}

//添加收藏
function addfav(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=addfav", data: "wid=" + wid, cache: false, success: function(data){$("body").append(data)}});});
};

//点出统计
function clickout(wid) {
	$(document).ready(function(){$.ajax({type: "GET", url: sitepath + "?mod=getdata&type=outstat", data: "wid=" + wid, cache: false, success: function(data){}});});
};

//错误报告
function report(obj, wid) {
	$(document).ready(function(){if (confirm("确认报告此错误吗？")){ $("#" + obj).html("正在提交，请稍候..."); $.ajax({type: "GET", url: sitepath + "?mod=getdata&type=error", data: "wid=" + wid, cache: false, success: function(data){$("#" + obj).html(data);}})};});
};

//验证码
function refreshimg(obj) {
	var randnum = Math.random();
	$("#" + obj).html('<img src="' + sitepath + 'source/include/captcha.php?s=' + randnum + '" align="absmiddle" alt="看不清楚?换一张" onclick="this.src+='+ randnum +'" style="cursor: pointer;">');
}

    // 显示打赏弹窗
    function showDonatePopup() {
        document.getElementById('donate-popup').style.display = 'flex';
    }

    // 关闭打赏弹窗
    function closeDonatePopup() {
        document.getElementById('donate-popup').style.display = 'none';
    }

    // 点击弹窗外部关闭
    window.onclick = function(event) {
        var popup = document.getElementById('donate-popup');
        if (event.target == popup) {
            popup.style.display = 'none';
        }
    }
    
// 推荐框颜色渐变    
    function generateColor(index) {
  // 使用 HSL 模式，根据索引生成不同的色调
  const hue = (index * 360) / 35; // 35 是你希望的颜色数量
  return `hsl(${hue}, 100%, 50%)`;
}

const items = document.querySelectorAll('#bestbox ul li');
items.forEach((item, index) => {
  item.style.backgroundColor = generateColor(index + 4); // +1 是因为索引从 0 开始
});

//首页轮播条
			function AutoScroll(obj){
                $(obj).find("ul:first").animate({
                        marginTop:"-22px"
                },500,function(){
                        $(this).css({marginTop:"0px"}).find("li:first").appendTo(this);
                });
			}
			$(document).ready(function(){
				timer=setInterval('AutoScroll(".site-notice")',4000);
			});
			$('#lunbo li').mousemove(function(){clearInterval(timer)});
			$('#lunbo li').mouseout(function(){timer=setInterval('AutoScroll(".site-notice")',5000)});
			

var _hmt = _hmt || [];
(function() {
  var hm = document.createElement("script");
  hm.src = "https://hm.baidu.com/hm.js?de6784c7f19b11f9d9d70711252011fe";
  var s = document.getElementsByTagName("script")[0]; 
  s.parentNode.insertBefore(hm, s);
})();

// 打赏JS
function showDonatePopup() {
document.getElementById('donate-popup').style.display = 'flex';
}

function closeDonatePopup() {
document.getElementById('donate-popup').style.display = 'none';
}



