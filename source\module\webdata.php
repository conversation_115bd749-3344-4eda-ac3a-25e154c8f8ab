<?php
/*
 * 网站数据获取模块 - 简单版本（第一版）
 * 恢复到最原始的简单收录量检测
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

/** 获取URL内容 - 增强版本，使用不同的函数名避免冲突 */
function get_url_content_enhanced($url, $timeout = 30) {
    // 记录调试信息
    error_log("正在获取URL内容: $url");

    // 方案1: 使用CURL
    if (extension_loaded('curl')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // 支持gzip压缩

        // 设置完整的浏览器请求头
        $headers = array(
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Upgrade-Insecure-Requests: 1'
        );

        // 如果是百度搜索，添加特定的请求头
        if (strpos($url, 'baidu.com') !== false) {
            $headers[] = 'Referer: https://www.baidu.com/';
            $headers[] = 'Sec-Fetch-Dest: document';
            $headers[] = 'Sec-Fetch-Mode: navigate';
            $headers[] = 'Sec-Fetch-Site: same-origin';
            $headers[] = 'Sec-Fetch-User: ?1';
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($data && $http_code == 200) {
            error_log("CURL获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("CURL获取失败: $url, HTTP状态码: $http_code, 错误: $error");
        }
    }

    // 方案2: 使用file_get_contents (如果允许URL fopen)
    if (ini_get('allow_url_fopen')) {
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
        ]);

        $data = @file_get_contents($url, false, $context);
        if ($data) {
            error_log("file_get_contents获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("file_get_contents获取失败: $url");
        }
    }

    error_log("所有方法都失败了: $url");
    return false;
}

/** 百度收录量（原PageRank字段） - 增强版本 */
function get_pagerank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取百度收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slbaidu.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10); // 减少超时时间到10秒

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("百度收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("百度收录量API频率限制 - 域名: $clean_url, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
                // API频率限制时，直接返回0，不再尝试其他方法
                return 0;
            } else {
                error_log("百度收录量API错误 - 域名: $clean_url, 错误码: {$result['code']}, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
            }
        }
    }

    // 方案2: 直接搜索百度（备用方案）
    $search_url = "https://www.baidu.com/s?wd=site:" . urlencode($clean_url);
    error_log("尝试直接搜索百度: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("百度搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/百度为您找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到约\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("百度收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("百度收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 必应收录量（原百度权重字段） - 增强版本 */
function get_baidurank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取必应收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slbiying.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("必应收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("必应收录量API频率限制 - 域名: $clean_url, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
                return 0;
            } else {
                error_log("必应收录量API错误 - 域名: $clean_url, 错误码: {$result['code']}, 消息: " . (isset($result['msg']) ? $result['msg'] : '未知'));
            }
        }
    }

    // 方案2: 直接搜索必应（备用方案）
    $search_url = "https://www.bing.com/search?q=site:" . urlencode($clean_url);
    error_log("尝试直接搜索必应: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("必应搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/([0-9,，\s]+)\s*results/i',
        '/([0-9,，\s]+)\s*个结果/u',
        '/About\s*([0-9,，\s]+)\s*results/i',
        '/共找到\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("必应收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("必应收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 360收录量（原搜狗权重字段） - 增强版本 */
function get_sogourank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取360收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/sl360.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("360收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("360收录量API频率限制 - 域名: $clean_url");
                return 0;
            } else {
                error_log("360收录量API失败 - 域名: $clean_url, 响应: " . substr($api_data, 0, 200));
            }
        }
    }

    // 方案2: 直接搜索360（备用方案）
    $search_url = "https://www.so.com/s?q=site:" . urlencode($clean_url);
    error_log("尝试直接搜索360: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("360搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到约\s*([0-9,，\s]+)\s*条结果/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("360收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("360收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 搜狗收录量（原Alexa排名字段） - 增强版本 */
function get_alexarank($url) {
    $clean_url = preg_replace('/^https?:\/\//', '', $url);
    $clean_url = preg_replace('/^www\./', '', $clean_url);
    $clean_url = rtrim($clean_url, '/');

    error_log("开始获取搜狗收录量 - 域名: $clean_url");

    // 方案1: 使用API接口（推荐）
    $api_url = "https://cn.apihz.cn/api/wangzhan/slsougou.php?id=10004250&key=4faeb28ed2ca8ac3fbfbd12ebd332f46&domain=" . urlencode($clean_url);
    $api_data = get_url_content_enhanced($api_url, 10);

    if ($api_data) {
        $result = json_decode($api_data, true);
        if (isset($result['code'])) {
            if ($result['code'] == 200 && isset($result['num'])) {
                $count = intval($result['num']);
                error_log("搜狗收录量API获取成功 - 域名: $clean_url, 收录量: $count");
                return $count;
            } elseif ($result['code'] == 400) {
                error_log("搜狗收录量API频率限制 - 域名: $clean_url");
                return 0;
            } else {
                error_log("搜狗收录量API失败 - 域名: $clean_url, 响应: " . substr($api_data, 0, 200));
            }
        }
    }

    // 方案2: 直接搜索搜狗（备用方案）
    $search_url = "https://www.sogou.com/web?query=site:" . urlencode($clean_url);
    error_log("尝试直接搜索搜狗: $search_url");

    $data = get_url_content_enhanced($search_url, 10);
    if (!$data || strlen($data) < 200) {
        error_log("搜狗搜索页面获取失败或内容过短 - 域名: $clean_url");
        return 0;
    }

    // 多种匹配模式
    $patterns = array(
        '/找到约\s*([0-9,，\s]+)\s*条结果/u',
        '/相关结果约\s*([0-9,，\s]+)\s*个/u',
        '/找到相关结果约\s*([0-9,，\s]+)\s*个/u'
    );

    foreach ($patterns as $pattern) {
        if (preg_match($pattern, $data, $matches)) {
            $count = preg_replace('/[^0-9]/', '', $matches[1]);
            $count = intval($count);
            error_log("搜狗收录量直接搜索成功 - 域名: $clean_url, 收录量: $count, 匹配模式: $pattern");
            return $count;
        }
    }

    error_log("搜狗收录量获取失败 - 域名: $clean_url, 所有方法都失败");
    return 0;
}

/** 获取网站标题 */
function get_title($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $data, $matches)) {
        $title = trim($matches[1]);
        $title = html_entity_decode($title, ENT_QUOTES, 'UTF-8');
        return $title;
    }
    
    return '';
}

/** 获取网站描述 */
function get_description($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta description
    if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    // 如果没有找到meta description，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']description["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    return '';
}

/** 获取网站关键词 */
function get_keywords($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta keywords
    if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    // 如果没有找到meta keywords，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']keywords["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    return '';
}

/** 获取所有网站数据 */
function get_all_website_data($url) {
    $data = array(
        'title' => get_title($url),
        'description' => get_description($url),
        'keywords' => get_keywords($url),
        'baidu_index' => get_pagerank($url),
        'bing_index' => get_baidurank($url),
        '360_index' => get_sogourank($url),
        'sogou_index' => get_alexarank($url),
        'check_time' => date('Y-m-d H:i:s')
    );

    return $data;
}

/** 获取META信息 - 加强优化版本 */
function get_sitemeta($url) {
	$original_url = $url;
	$url = format_url($url);

	// 记录开始时间
	$start_time = microtime(true);
	error_log("开始获取Meta信息 - URL: $url");

	$meta = array('title' => '', 'keywords' => '', 'description' => '');
	$data = false;

	// 策略1: 使用增强的Meta抓取函数
	$data = get_url_content_for_meta_enhanced($url);

	// 策略2: 如果失败，尝试不同的协议
	if (!$data) {
		$alt_url = str_replace('https://', 'http://', $url);
		if ($alt_url != $url) {
			error_log("Meta抓取 - 尝试HTTP协议: $alt_url");
			$data = get_url_content_for_meta_enhanced($alt_url);
		}
	}

	// 策略3: 如果还是失败，尝试添加www前缀
	if (!$data && !preg_match('/^https?:\/\/www\./', $url)) {
		$www_url = preg_replace('/^(https?:\/\/)/', '$1www.', $url);
		error_log("Meta抓取 - 尝试www前缀: $www_url");
		$data = get_url_content_for_meta_enhanced($www_url);
	}

	// 策略4: 如果有www，尝试去掉www
	if (!$data && preg_match('/^https?:\/\/www\./', $url)) {
		$no_www_url = preg_replace('/^(https?:\/\/)www\./', '$1', $url);
		error_log("Meta抓取 - 尝试去掉www: $no_www_url");
		$data = get_url_content_for_meta_enhanced($no_www_url);
	}

	// 策略5: 备用方案 - 使用原有的get_url_content函数
	if (!$data) {
		error_log("Meta抓取 - 尝试备用方案: 使用原有函数");
		$data = get_url_content($url);
	}

	// 策略6: 尝试简单的file_get_contents
	if (!$data && ini_get('allow_url_fopen')) {
		error_log("Meta抓取 - 尝试file_get_contents备用方案");
		$context = stream_context_create(array(
			'http' => array(
				'timeout' => 10,
				'user_agent' => 'Mozilla/5.0 (compatible; MetaCrawler/1.0)',
				'header' => "Accept: text/html\r\n"
			)
		));
		$data = @file_get_contents($url, false, $context);
	}

	// 策略7: 源码查看备用方案
	if (!$data) {
		$data = get_meta_from_source_view($url);
	}

	if (!empty($data)) {
		$meta = extract_meta_from_content($data);

		// 如果仍然没有获取到任何Meta信息，尝试更宽松的提取
		if (empty($meta['title']) && empty($meta['keywords']) && empty($meta['description'])) {
			error_log("Meta抓取 - 尝试宽松模式提取");
			$meta = extract_meta_loose_mode($data);
		}
	} else {
		error_log("Meta抓取失败 - URL: $url, 所有策略都失败");
	}

	// 记录结束时间和结果
	$end_time = microtime(true);
	$duration = round(($end_time - $start_time) * 1000, 2);
	$title_preview = !empty($meta['title']) ? substr($meta['title'], 0, 50) : '(无标题)';
	error_log("Meta抓取完成 - URL: $url, 耗时: {$duration}ms, Title: $title_preview");

	// 清理和验证结果
	$meta = clean_and_validate_meta($meta);

	return $meta;
}

/** 从HTML内容中提取Meta信息 */
function extract_meta_from_content($data) {
	$meta = array('title' => '', 'keywords' => '', 'description' => '');

	// 清理数据，移除多余的空白字符，但保留换行用于调试
	$data = preg_replace('/[ \t]+/', ' ', $data);

	// 提取Title - 支持更多格式
	$title_patterns = array(
		'/<title[^>]*>(.*?)<\/title>/si',
		'/<h1[^>]*>(.*?)<\/h1>/si', // 备用：如果没有title标签，尝试h1
	);

	foreach ($title_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$title = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($title)) {
				$meta['title'] = $title;
				break;
			}
		}
	}

	// 提取Keywords - 支持更多格式和大小写，包括无引号格式
	$keywords_patterns = array(
		// 标准格式 - 双引号
		'/<meta\s+name=["\']keywords["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+content=["\'](.*?)["\']\s+name=["\']keywords["\']/si',

		// 无引号格式
		'/<meta\s+name=keywords\s+content=["\'](.*?)["\']/si',
		'/<meta\s+content=["\'](.*?)["\']\s+name=keywords/si',

		// HTTP-EQUIV格式
		'/<meta\s+http-equiv=["\']keywords["\']\s+content=["\'](.*?)["\']/si',

		// Property格式
		'/<meta\s+property=["\']keywords["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+property=["\']article:tag["\']\s+content=["\'](.*?)["\']/si',

		// 新闻关键词
		'/<meta\s+name=["\']news_keywords["\']\s+content=["\'](.*?)["\']/si',

		// 其他可能的格式
		'/<meta\s+name=["\']keyword["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+name=["\']tags["\']\s+content=["\'](.*?)["\']/si'
	);

	foreach ($keywords_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$keywords = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($keywords) && strlen($keywords) > 2) {
				$meta['keywords'] = $keywords;
				break;
			}
		}
	}

	// 提取Description - 支持更多格式和大小写，包括无引号格式
	$description_patterns = array(
		// 标准格式 - 双引号
		'/<meta\s+name=["\']description["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+content=["\'](.*?)["\']\s+name=["\']description["\']/si',

		// 无引号格式
		'/<meta\s+name=description\s+content=["\'](.*?)["\']/si',
		'/<meta\s+content=["\'](.*?)["\']\s+name=description/si',

		// HTTP-EQUIV格式
		'/<meta\s+http-equiv=["\']description["\']\s+content=["\'](.*?)["\']/si',

		// Property格式 (OpenGraph等)
		'/<meta\s+property=["\']description["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+property=["\']og:description["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+property=["\']twitter:description["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+property=["\']article:description["\']\s+content=["\'](.*?)["\']/si',

		// 其他可能的格式
		'/<meta\s+name=["\']summary["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+name=["\']abstract["\']\s+content=["\'](.*?)["\']/si',
		'/<meta\s+name=["\']subject["\']\s+content=["\'](.*?)["\']/si'
	);

	foreach ($description_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$description = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($description) && strlen($description) > 10) {
				$meta['description'] = $description;
				break;
			}
		}
	}

	// 如果还是没有描述，尝试从第一个段落提取
	if (empty($meta['description'])) {
		if (preg_match('/<p[^>]*>(.*?)<\/p>/si', $data, $matches)) {
			$description = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (strlen($description) > 20 && strlen($description) < 300) {
				$meta['description'] = $description;
			}
		}
	}

	return $meta;
}

/** 宽松模式Meta提取 - 用于处理特殊格式或损坏的HTML */
function extract_meta_loose_mode($data) {
	$meta = array('title' => '', 'keywords' => '', 'description' => '');

	error_log("Meta抓取 - 开始宽松模式提取");

	// 宽松的Title提取
	$title_patterns = array(
		'/<title[^>]*>(.*?)<\/title>/si',
		'/<title[^>]*>(.*?)$/si', // 没有结束标签的情况
		'/title[^>]*content[^>]*["\']([^"\']+)["\']>/si', // Meta格式的title
		'/<h1[^>]*>(.*?)<\/h1>/si',
		'/<h2[^>]*>(.*?)<\/h2>/si'
	);

	foreach ($title_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$title = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($title) && strlen($title) > 2 && strlen($title) < 200) {
				$meta['title'] = $title;
				break;
			}
		}
	}

	// 宽松的Keywords提取 - 使用更灵活的正则
	$keywords_patterns = array(
		'/keywords[^>]*content[^>]*["\']([^"\']+)["\']>/si',
		'/content[^>]*["\']([^"\']*(?:关键词|keywords|tags)[^"\']*)["\']>/si',
		'/name[^>]*keywords[^>]*content[^>]*["\']([^"\']+)["\']>/si',
		'/关键词[^>]*["\']([^"\']+)["\']>/si'
	);

	foreach ($keywords_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$keywords = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($keywords) && strlen($keywords) > 3 && strlen($keywords) < 300) {
				$meta['keywords'] = $keywords;
				break;
			}
		}
	}

	// 宽松的Description提取
	$description_patterns = array(
		'/description[^>]*content[^>]*["\']([^"\']+)["\']>/si',
		'/content[^>]*["\']([^"\']*(?:描述|description|简介)[^"\']*)["\']>/si',
		'/name[^>]*description[^>]*content[^>]*["\']([^"\']+)["\']>/si',
		'/描述[^>]*["\']([^"\']+)["\']>/si',
		'/简介[^>]*["\']([^"\']+)["\']>/si'
	);

	foreach ($description_patterns as $pattern) {
		if (preg_match($pattern, $data, $matches)) {
			$description = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (!empty($description) && strlen($description) > 10 && strlen($description) < 500) {
				$meta['description'] = $description;
				break;
			}
		}
	}

	// 如果还是没有描述，尝试从页面内容中提取
	if (empty($meta['description'])) {
		// 查找第一个有意义的段落
		if (preg_match('/<p[^>]*>([^<]{20,200})<\/p>/si', $data, $matches)) {
			$description = trim(html_entity_decode(strip_tags($matches[1]), ENT_QUOTES, 'UTF-8'));
			if (strlen($description) > 20 && !preg_match('/^\s*(版权|copyright|all rights)/i', $description)) {
				$meta['description'] = $description;
			}
		}
	}

	error_log("Meta抓取 - 宽松模式结果: Title=" . substr($meta['title'], 0, 30) .
			  ", Keywords=" . substr($meta['keywords'], 0, 30) .
			  ", Description=" . substr($meta['description'], 0, 30));

	return $meta;
}

/** 源码查看备用方案 - 专门用于获取Meta信息 */
function get_meta_from_source_view($url) {
	error_log("Meta抓取 - 尝试源码查看备用方案: $url");

	// 尝试多种源码查看方式
	$source_urls = array();

	// 方式1: 直接查看源码
	$source_urls[] = $url;

	// 方式2: 如果是HTTPS，尝试HTTP
	if (strpos($url, 'https://') === 0) {
		$source_urls[] = str_replace('https://', 'http://', $url);
	}

	// 方式3: 添加view-source前缀（某些浏览器支持）
	$source_urls[] = 'view-source:' . $url;

	foreach ($source_urls as $test_url) {
		$data = get_simple_url_content($test_url);
		if ($data && strlen($data) > 100) {
			// 检查是否包含HTML标签
			if (preg_match('/<meta\s+/i', $data) || preg_match('/<title\s*>/i', $data)) {
				error_log("Meta抓取 - 源码查看成功: $test_url");
				return $data;
			}
		}
	}

	return false;
}

/** 简单的URL内容获取函数 - 用于备用方案 */
function get_simple_url_content($url, $timeout = 8) {
	if (!function_exists('curl_init')) {
		return false;
	}

	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5);
	curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
	curl_setopt($ch, CURLOPT_MAXREDIRS, 2);
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
	curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; SimpleBot/1.0)');
	curl_setopt($ch, CURLOPT_HTTPHEADER, array(
		'Accept: text/html,text/plain,*/*',
		'Connection: close'
	));

	$data = curl_exec($ch);
	$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
	$error = curl_error($ch);
	curl_close($ch);

	if ($error || $http_code < 200 || $http_code >= 400) {
		return false;
	}

	return $data;
}

/** 清理和验证Meta数据 */
function clean_and_validate_meta($meta) {
	foreach ($meta as $key => $value) {
		if (is_string($value)) {
			// 移除多余的空白字符
			$value = preg_replace('/\s+/', ' ', trim($value));

			// 移除特殊字符和控制字符
			$value = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $value);

			// 限制长度
			if ($key == 'title') {
				if (strlen($value) > 100) {
					$value = mb_substr($value, 0, 100, 'UTF-8') . '...';
				}
			} elseif ($key == 'description') {
				if (strlen($value) > 200) {
					$value = mb_substr($value, 0, 200, 'UTF-8') . '...';
				}
			} elseif ($key == 'keywords') {
				if (strlen($value) > 150) {
					$value = mb_substr($value, 0, 150, 'UTF-8');
				}
				// 清理关键词格式
				$value = preg_replace('/[,，]\s*/', ',', $value);
				$value = trim($value, ',');
			}

			$meta[$key] = $value;
		}
	}

	return $meta;
}

/** 增强版Meta抓取URL内容获取函数 */
function get_url_content_for_meta_enhanced($url, $timeout = 15) {
    error_log("Meta抓取增强版 - 开始获取URL内容: $url");

    if (empty($url)) {
        return false;
    }

    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        error_log("Meta抓取增强版 - URL格式无效: $url");
        return false;
    }

    $data = '';
    $user_agents = array(
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    );

    // 尝试不同的抓取策略
    $strategies = array(
        array('range' => true, 'timeout' => 8),   // 策略1: 快速抓取前64KB
        array('range' => false, 'timeout' => 15)  // 策略2: 完整抓取
    );

    foreach ($strategies as $strategy) {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 3);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_TIMEOUT, $strategy['timeout']);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 8);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            // 随机User-Agent
            curl_setopt($ch, CURLOPT_USERAGENT, $user_agents[array_rand($user_agents)]);

            // 更完整的请求头
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                'Accept-Encoding: gzip, deflate, br',
                'DNT: 1',
                'Connection: keep-alive',
                'Upgrade-Insecure-Requests: 1',
                'Sec-Fetch-Dest: document',
                'Sec-Fetch-Mode: navigate',
                'Sec-Fetch-Site: none'
            ));

            // 处理压缩
            curl_setopt($ch, CURLOPT_ENCODING, '');

            // 根据策略决定是否使用Range请求
            if ($strategy['range']) {
                curl_setopt($ch, CURLOPT_RANGE, '0-131071'); // 前128KB
            }

            $data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            $content_type = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
            curl_close($ch);

            // 检查CURL错误
            if ($curl_error) {
                error_log("Meta抓取增强版 - CURL错误: $curl_error");
                continue; // 尝试下一个策略
            }

            // 检查内容类型
            if ($content_type && !preg_match('/text\/html|application\/xhtml/i', $content_type)) {
                error_log("Meta抓取增强版 - 非HTML内容: $content_type");
                continue;
            }

            // 检查HTTP状态码
            if ($http_code >= 200 && $http_code < 400) {
                if (strlen($data) >= 100) {
                    break; // 成功获取到内容
                } else {
                    error_log("Meta抓取增强版 - 内容太短: " . strlen($data) . " bytes");
                }
            } else {
                error_log("Meta抓取增强版 - HTTP错误: $http_code");
            }

            $data = false;
        } else {
            error_log("Meta抓取增强版 - CURL扩展不可用");
            return false;
        }
    }

    if (!$data) {
        error_log("Meta抓取增强版 - 所有策略都失败: $url");
        return false;
    }

    // 编码检测和转换
    $detected_encoding = mb_detect_encoding($data, array('UTF-8', 'GB2312', 'GBK', 'BIG5', 'ISO-8859-1'), true);
    if ($detected_encoding && $detected_encoding != 'UTF-8') {
        $converted_data = @mb_convert_encoding($data, 'UTF-8', $detected_encoding);
        if ($converted_data) {
            $data = $converted_data;
            error_log("Meta抓取增强版 - 编码转换: $detected_encoding -> UTF-8");
        }
    }

    error_log("Meta抓取增强版 - 获取成功: $url, 内容长度: " . strlen($data) . " bytes");
    return $data;
}

/** 专门用于Meta抓取的URL内容获取函数 - 使用更短的超时时间 */
function get_url_content_for_meta($url, $timeout = 10) {
    error_log("Meta抓取 - 开始获取URL内容: $url");

    if (empty($url)) {
        return false;
    }

    // 智能处理URL协议
    if (substr($url, 0, 7) != 'http://' && substr($url, 0, 8) != 'https://') {
        $url = 'https://' . $url;
    }

    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        error_log("Meta抓取 - URL格式无效: $url");
        return false;
    }

    $data = '';
    $max_retries = 1; // Meta抓取只重试1次

    for ($i = 0; $i < $max_retries && empty($data); $i++) {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 2); // 减少重定向次数
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout); // 使用更短的超时时间
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 连接超时5秒
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            // 轻量级User-Agent
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MetaCrawler/1.0)');

            // 简化的请求头
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Accept: text/html,application/xhtml+xml,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Connection: close'
            ));

            // 只获取前64KB内容（Meta信息通常在页面头部）
            curl_setopt($ch, CURLOPT_RANGE, '0-65535');

            $data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            // 检查CURL错误
            if ($curl_error) {
                error_log("Meta抓取 - CURL错误: $curl_error");
                $data = false;
                continue;
            }

            // 检查HTTP状态码
            if ($http_code >= 200 && $http_code < 400) {
                if (strlen($data) < 50) {
                    error_log("Meta抓取 - 内容太短: " . strlen($data) . " bytes");
                    $data = false;
                    continue;
                }
            } else {
                error_log("Meta抓取 - HTTP错误: $http_code");
                $data = false;
            }
        } else {
            error_log("Meta抓取 - CURL扩展不可用");
            return false;
        }
    }

    if (!$data) {
        error_log("Meta抓取 - 获取失败: $url");
        return false;
    }

    // 编码转换
    $encode = mb_detect_encoding($data, array('ascii', 'gb2312', 'utf-8', 'gbk'));
    if ($encode == 'EUC-CN' || $encode == 'CP936') {
        $data = @mb_convert_encoding($data, 'utf-8', 'gb2312');
    }

    error_log("Meta抓取 - 获取成功: $url, 内容长度: " . strlen($data) . " bytes");
    return $data;
}

/** Server IP */
function get_serverip($url) {
	$domain = get_domain($url);
	if ($domain) {
		$ip = gethostbyname($domain);
	} else {
		$ip = 0;
	}

	return $ip;
}

?>
