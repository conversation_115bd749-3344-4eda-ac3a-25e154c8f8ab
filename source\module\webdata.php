<?php
/*
 * 网站数据获取模块 - 简单版本（第一版）
 * 恢复到最原始的简单收录量检测
 */

if (!defined('IN_IWEBDIR')) exit('Access Denied');

/** 获取URL内容 - 增强版本，使用不同的函数名避免冲突 */
function get_url_content_enhanced($url, $timeout = 30) {
    // 记录调试信息
    error_log("正在获取URL内容: $url");

    // 方案1: 使用CURL
    if (extension_loaded('curl')) {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, ''); // 支持gzip压缩

        // 设置完整的浏览器请求头
        $headers = array(
            'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding: gzip, deflate, br',
            'Cache-Control: no-cache',
            'Pragma: no-cache',
            'Upgrade-Insecure-Requests: 1'
        );

        // 如果是百度搜索，添加特定的请求头
        if (strpos($url, 'baidu.com') !== false) {
            $headers[] = 'Referer: https://www.baidu.com/';
            $headers[] = 'Sec-Fetch-Dest: document';
            $headers[] = 'Sec-Fetch-Mode: navigate';
            $headers[] = 'Sec-Fetch-Site: same-origin';
            $headers[] = 'Sec-Fetch-User: ?1';
        }

        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);

        $data = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        if ($data && $http_code == 200) {
            error_log("CURL获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("CURL获取失败: $url, HTTP状态码: $http_code, 错误: $error");
        }
    }

    // 方案2: 使用file_get_contents (如果允许URL fopen)
    if (ini_get('allow_url_fopen')) {
        $context = stream_context_create([
            'http' => [
                'timeout' => $timeout,
                'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
        ]);

        $data = @file_get_contents($url, false, $context);
        if ($data) {
            error_log("file_get_contents获取成功: $url, 数据长度: " . strlen($data));
            return $data;
        } else {
            error_log("file_get_contents获取失败: $url");
        }
    }

    error_log("所有方法都失败了: $url");
    return false;
}

/** Google Pagerank */
function get_pagerank($url) {
    $data = get_url_content("http://pr.links.cn/getpr.asp?queryurl=$url&show=1");
    if (preg_match('/<a(.*?)>(\d+)<\/a>/i', $data, $matches)) {
        $rank = intval($matches[2]);
    } else {
        $rank = 0;
    }
    return $rank;
}

/** Baidu Pagerank */
function get_baidurank($url) {
	$data = get_url_content("http://www.aizhan.com/getbr.php?url=$url&style=1");
	if (preg_match('/<a(.*?)>(\d+)<\/a>/i', $data, $matches)) {
		$rank = intval($matches[2]);
	} else {
		$rank = 0;
	}
	return $rank;
}

/** Sogou Pagerank */
function get_sogourank($url) {
	$data = get_url_content("http://rank.ie.sogou.com/sogourank.php?ur=$url");
	if (preg_match('/sogourank=(\d+)/i', $data, $matches)) {
		$rank = intval($matches[1]);
	} else {
		$rank = 0;
	}
	return $rank;
}

/** Alexa Rank */
function get_alexarank($url) {
	$data = get_url_content("http://xml.alexa.com/data?cli=10&dat=nsa&ver=quirk-searchstatus&url=$url");
	if (preg_match('/<POPULARITY[^>]*URL[^>]*TEXT[^>]*\"([0-9]+)\"/i', $data, $matches)) {
		$rank = strip_tags($matches[1]);
	} else {
		$rank = 0;
	}
	return $rank;
}

/** 获取网站标题 */
function get_title($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    if (preg_match('/<title[^>]*>(.*?)<\/title>/is', $data, $matches)) {
        $title = trim($matches[1]);
        $title = html_entity_decode($title, ENT_QUOTES, 'UTF-8');
        return $title;
    }
    
    return '';
}

/** 获取网站描述 */
function get_description($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta description
    if (preg_match('/<meta[^>]*name=["\']description["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    // 如果没有找到meta description，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']description["\'][^>]*>/i', $data, $matches)) {
        $description = trim($matches[1]);
        $description = html_entity_decode($description, ENT_QUOTES, 'UTF-8');
        return $description;
    }
    
    return '';
}

/** 获取网站关键词 */
function get_keywords($url) {
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'http://' . $url;
    }
    
    $data = get_url_content($url);
    if (!$data) {
        return '';
    }
    
    // 查找meta keywords
    if (preg_match('/<meta[^>]*name=["\']keywords["\'][^>]*content=["\']([^"\']*)["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    // 如果没有找到meta keywords，尝试另一种格式
    if (preg_match('/<meta[^>]*content=["\']([^"\']*)["\'][^>]*name=["\']keywords["\'][^>]*>/i', $data, $matches)) {
        $keywords = trim($matches[1]);
        $keywords = html_entity_decode($keywords, ENT_QUOTES, 'UTF-8');
        return $keywords;
    }
    
    return '';
}

/** 获取所有网站数据 */
function get_all_website_data($url) {
    $data = array(
        'title' => get_title($url),
        'description' => get_description($url),
        'keywords' => get_keywords($url),
        'baidu_index' => get_pagerank($url),
        'bing_index' => get_baidurank($url),
        '360_index' => get_sogourank($url),
        'sogou_index' => get_alexarank($url),
        'check_time' => date('Y-m-d H:i:s')
    );

    return $data;
}

/** 获取META信息 */
function get_sitemeta($url) {
	$url = format_url($url);
	$data = get_url_content($url);
	$meta = array();
	if (!empty($data)) {
		#Title
		preg_match('/<TITLE>([\w\W]*?)<\/TITLE>/si', $data, $matches);
		if (!empty($matches[1])) {
			$meta['title'] = $matches[1];
		}

		#Keywords
		preg_match('/<META\s+name="keywords"\s+content="([\w\W]*?)"/si', $data, $matches);
		if (empty($matches[1])) {
			preg_match("/<META\s+name='keywords'\s+content='([\w\W]*?)'/si", $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+content="([\w\W]*?)"\s+name="keywords"/si', $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+http-equiv="keywords"\s+content="([\w\W]*?)"/si', $data, $matches);
		}
		if (!empty($matches[1])) {
			$meta['keywords'] = $matches[1];
		}

		#Description
		preg_match('/<META\s+name="description"\s+content="([\w\W]*?)"/si', $data, $matches);
		if (empty($matches[1])) {
			preg_match("/<META\s+name='description'\s+content='([\w\W]*?)'/si", $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+content="([\w\W]*?)"\s+name="description"/si', $data, $matches);
		}
		if (empty($matches[1])) {
			preg_match('/<META\s+http-equiv="description"\s+content="([\w\W]*?)"/si', $data, $matches);
		}
		if (!empty($matches[1])) {
			$meta['description'] = $matches[1];
		}
	}

	return $meta;
}

/** Server IP */

/** Server IP */
function get_url_content_for_meta($url, $timeout = 10) {
    error_log("Meta抓取 - 开始获取URL内容: $url");

    if (empty($url)) {
        return false;
    }

    // 智能处理URL协议
    if (substr($url, 0, 7) != 'http://' && substr($url, 0, 8) != 'https://') {
        $url = 'https://' . $url;
    }

    // 验证URL格式
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        error_log("Meta抓取 - URL格式无效: $url");
        return false;
    }

    $data = '';
    $max_retries = 1; // Meta抓取只重试1次

    for ($i = 0; $i < $max_retries && empty($data); $i++) {
        if (function_exists('curl_init')) {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_MAXREDIRS, 2); // 减少重定向次数
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($ch, CURLOPT_TIMEOUT, $timeout); // 使用更短的超时时间
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // 连接超时5秒
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

            // 轻量级User-Agent
            curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MetaCrawler/1.0)');

            // 简化的请求头
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Accept: text/html,application/xhtml+xml,*/*;q=0.8',
                'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
                'Connection: close'
            ));

            // 只获取前64KB内容（Meta信息通常在页面头部）
            curl_setopt($ch, CURLOPT_RANGE, '0-65535');

            $data = curl_exec($ch);
            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curl_error = curl_error($ch);
            curl_close($ch);

            // 检查CURL错误
            if ($curl_error) {
                error_log("Meta抓取 - CURL错误: $curl_error");
                $data = false;
                continue;
            }

            // 检查HTTP状态码
            if ($http_code >= 200 && $http_code < 400) {
                if (strlen($data) < 50) {
                    error_log("Meta抓取 - 内容太短: " . strlen($data) . " bytes");
                    $data = false;
                    continue;
                }
            } else {
                error_log("Meta抓取 - HTTP错误: $http_code");
                $data = false;
            }
        } else {
            error_log("Meta抓取 - CURL扩展不可用");
            return false;
        }
    }

    if (!$data) {
        error_log("Meta抓取 - 获取失败: $url");
        return false;
    }

    // 编码转换
    $encode = mb_detect_encoding($data, array('ascii', 'gb2312', 'utf-8', 'gbk'));
    if ($encode == 'EUC-CN' || $encode == 'CP936') {
        $data = @mb_convert_encoding($data, 'utf-8', 'gb2312');
    }

    error_log("Meta抓取 - 获取成功: $url, 内容长度: " . strlen($data) . " bytes");
    return $data;
}

/** Server IP */
function get_serverip($url) {
	$domain = get_domain($url);
	if ($domain) {
		$ip = gethostbyname($domain);
	} else {
		$ip = 0;
	}

	return $ip;
}

?>
