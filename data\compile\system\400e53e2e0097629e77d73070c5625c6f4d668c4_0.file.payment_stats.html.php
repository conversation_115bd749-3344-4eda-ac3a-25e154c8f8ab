<?php
/* Smarty version 4.5.5, created on 2025-07-21 22:03:13
  from '/www/wwwroot/www.95dir.com/themes/system/payment_stats.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_687e48a11106c0_69143858',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '400e53e2e0097629e77d73070c5625c6f4d668c4' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/payment_stats.html',
      1 => 1753106578,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_687e48a11106c0_69143858 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_checkPlugins(array(0=>array('file'=>'/www/wwwroot/www.95dir.com/source/extend/smarty/plugins/modifier.truncate.php','function'=>'smarty_modifier_truncate',),));
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<div class="main">
    <div class="title">
        <h2>付费统计</h2>
    </div>
    
    <!-- 统计概览 -->
    <div class="stats-overview" style="margin-bottom: 20px;">
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">统计概览</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #007bff;">
                    <strong>总记录数：</strong><span style="color: #007bff; font-size: 18px;"><?php echo $_smarty_tpl->tpl_vars['stats']->value['total_count'];?>
</span>
                </div>
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #28a745;">
                    <strong>总金额：</strong><span style="color: #28a745; font-size: 18px;">￥<?php echo $_smarty_tpl->tpl_vars['stats']->value['total_amount'];?>
</span>
                </div>
            </div>
        </div>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">按类型统计</h4>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['type_stats']->value, 'stat');
$_smarty_tpl->tpl_vars['stat']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['stat']->value) {
$_smarty_tpl->tpl_vars['stat']->do_else = false;
?>
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; min-width: 150px;">
                    <div style="font-weight: bold; color: #333;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['name'];?>
</div>
                    <div style="color: #666; font-size: 14px;">实际数量: <span style="color: #28a745; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['stat']->value['count'];?>
</span></div>
                    <div style="color: #666; font-size: 14px;">记录数量: <?php echo $_smarty_tpl->tpl_vars['stat']->value['record_count'];?>
</div>
                    <div style="color: #666; font-size: 14px;">金额: ￥<?php echo $_smarty_tpl->tpl_vars['stat']->value['amount'];?>
</div>
                </div>
                <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
            </div>
            <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">
                <div style="color: #856404; font-size: 14px;">
                    <strong>💡 提示：</strong>推荐统计不包含VIP+推荐的网站，避免重复计费。如果实际数量与记录数量不匹配，可能存在历史数据问题。
                    <a href="payment_fix.php" style="color: #007bff; margin-left: 10px;">点击检查和修复数据</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选表单 -->
    <div class="filter-form" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <form method="get" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div>
                    <label>付费类型：</label>
                    <select name="type" style="padding: 5px;">
                        <option value="0">全部</option>
                        <option value="1" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 1) {?>selected<?php }?>>VIP</option>
                        <option value="2" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 2) {?>selected<?php }?>>推荐</option>
                        <option value="3" <?php if ($_smarty_tpl->tpl_vars['payment_type']->value == 3) {?>selected<?php }?>>快审</option>
                    </select>
                </div>
                <div>
                    <label>开始日期：</label>
                    <input type="date" name="start_date" value="<?php echo $_smarty_tpl->tpl_vars['start_date']->value;?>
" style="padding: 5px;">
                </div>
                <div>
                    <label>结束日期：</label>
                    <input type="date" name="end_date" value="<?php echo $_smarty_tpl->tpl_vars['end_date']->value;?>
" style="padding: 5px;">
                </div>
                <div>
                    <label>关键词：</label>
                    <input type="text" name="keywords" value="<?php echo $_smarty_tpl->tpl_vars['keywords']->value;?>
" placeholder="网站名称或网址" style="padding: 5px; width: 150px;">
                </div>
                <div>
                    <input type="submit" value="筛选" class="btn" style="padding: 6px 15px;">
                    <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
" class="btn" style="padding: 6px 15px; margin-left: 5px;">重置</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 记录列表 -->
    <div class="list">
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="60">ID</th>
                <th width="120">网站名称</th>
                <th width="150">网站网址</th>
                <th width="80">付费类型</th>
                <th width="80">单价</th>
                <th width="80">付费金额</th>
                <th width="140">付费时间</th>
                <th width="140">到期时间</th>
                <th width="80">状态</th>
                <th width="80">操作员</th>
                <th>备注</th>
            </tr>
            <?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['records']->value, 'record');
$_smarty_tpl->tpl_vars['record']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['record']->value) {
$_smarty_tpl->tpl_vars['record']->do_else = false;
?>
            <tr bgcolor="#FFFFFF">
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['record']->value['id'];?>
</td>
                <td><a href="../?mod=website_detail&id=<?php echo $_smarty_tpl->tpl_vars['record']->value['web_id'];?>
" target="_blank" title="查看网站详情"><?php echo smarty_modifier_truncate($_smarty_tpl->tpl_vars['record']->value['web_name'],20);?>
</a></td>
                <td><a href="<?php echo $_smarty_tpl->tpl_vars['record']->value['web_url'];?>
" target="_blank" title="访问网站"><?php echo smarty_modifier_truncate($_smarty_tpl->tpl_vars['record']->value['web_url'],25);?>
</a></td>
                <td align="center"><span style="color: <?php echo $_smarty_tpl->tpl_vars['record']->value['type_color'];?>
; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['record']->value['type_name'];?>
</span></td>
                <td align="center" style="color: #666; font-size: 12px;"><?php echo $_smarty_tpl->tpl_vars['record']->value['unit_price'];?>
</td>
                <td align="center" style="color: #28a745; font-weight: bold;">￥<?php echo $_smarty_tpl->tpl_vars['record']->value['payment_amount'];?>
</td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['record']->value['payment_date'];?>
</td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['record']->value['expire_date'];?>
</td>
                <td align="center"><span style="color: <?php echo $_smarty_tpl->tpl_vars['record']->value['status_color'];?>
; font-weight: bold;"><?php echo $_smarty_tpl->tpl_vars['record']->value['status_name'];?>
</span></td>
                <td align="center"><?php echo $_smarty_tpl->tpl_vars['record']->value['operator'];?>
</td>
                <td><?php echo smarty_modifier_truncate($_smarty_tpl->tpl_vars['record']->value['remark'],30);?>
</td>
            </tr>
            <?php
}
if ($_smarty_tpl->tpl_vars['record']->do_else) {
?>
            <tr bgcolor="#FFFFFF">
                <td colspan="11" align="center" style="padding: 30px; color: #999;">暂无付费记录</td>
            </tr>
            <?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php if ($_smarty_tpl->tpl_vars['showpage']->value) {?>
    <div class="pages">
        <?php echo $_smarty_tpl->tpl_vars['showpage']->value;?>

    </div>
    <?php }?>
</div>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
