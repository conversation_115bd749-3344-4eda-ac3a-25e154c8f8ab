<?php
/* Smarty version 4.5.5, created on 2025-07-13 00:23:51
  from '/www/wwwroot/www.95dir.com/themes/system/cache.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_68728c17a68736_82983609',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '43ea31acb46353ce272d491f1340946cbc1b49c6' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/cache.html',
      1 => 1739588647,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_68728c17a68736_82983609 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'info') {?>
    <h3 class="title"><em><?php echo $_smarty_tpl->tpl_vars['pagetitle']->value;?>
</em></h3>
	<div class="listbox">                    
        <form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
        <div class="toolbar">
			<select name="act" id="act" class="sel">
			<option value="update_static">更新静态缓存</option>
			</select>
			<input type="submit" class="btn" value="应用" onClick="return confirm('确认执行此操作吗？');">
        </div>
        
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
			<tr>
				<th>缓存名称</th>
				<th>缓存说明</th>
				<th>生成时间</th>
				<th>修改时间</th>
				<th>缓存大小</th>
				<th>操作选项</th>
			</tr>
			<?php
$_from = $_smarty_tpl->smarty->ext->_foreach->init($_smarty_tpl, $_smarty_tpl->tpl_vars['caches']->value, 'cache');
$_smarty_tpl->tpl_vars['cache']->do_else = true;
if ($_from !== null) foreach ($_from as $_smarty_tpl->tpl_vars['cache']->value) {
$_smarty_tpl->tpl_vars['cache']->do_else = false;
?>
			<tr>
				<td><?php echo $_smarty_tpl->tpl_vars['cache']->value['name'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['cache']->value['desc'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['cache']->value['ctime'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['cache']->value['mtime'];?>
</td>
				<td><?php echo $_smarty_tpl->tpl_vars['cache']->value['size'];?>
</td>
				<td><a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=show&cache_id=<?php echo $_smarty_tpl->tpl_vars['cache']->value['name'];?>
">查看</a> | <a href="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
?act=update&cache_id=<?php echo $_smarty_tpl->tpl_vars['cache']->value['name'];?>
">更新</a></td>
			</tr>
			<?php
}
$_smarty_tpl->smarty->ext->_foreach->restore($_smarty_tpl, 1);?>
		</table>
		</form>
	</div>
	<?php }?>

	<?php if ($_smarty_tpl->tpl_vars['action']->value == 'show') {?>
	<div class="formbox">
		<form name="mform" method="post" action="<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
">
		<table width="100%" border="0" cellspacing="1" cellpadding="0">
        	<tr>
            	<th>&nbsp;</th>
            	<td><pre><?php echo $_smarty_tpl->tpl_vars['data']->value;?>
</pre></td>
            </tr>
            <tr class="btnbox">
            	<th>&nbsp;</th>
				<td>
					<input name="act" type="hidden" id="act" value="<?php echo $_smarty_tpl->tpl_vars['h_action']->value;?>
">
					<input name="cache_id" type="hidden" id="cache_id" value="<?php echo $_smarty_tpl->tpl_vars['cache']->value['cache_id'];?>
">
					<input name="submit" type="submit" class="btn" value="更 新">&nbsp;
					<input name="reset" type="reset" class="btn" value="取 消" onClick="window.location.href='<?php echo $_smarty_tpl->tpl_vars['fileurl']->value;?>
';">
                </td>
            </tr>
        </table>
        </form>
	</div>
	<?php }?>
            
<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
