<?php
// 测试爬虫统计功能
require('init.php');
require('module/spider_tracker.php');

echo "<h2>爬虫统计功能测试</h2>";

// 测试1: 检查表是否存在
echo "<h3>1. 检查数据库表</h3>";
$table = $DB->table('spider_stats');
$check_sql = "SHOW TABLES LIKE '$table'";
$check_result = $DB->query($check_sql);
if ($DB->num_rows($check_result) > 0) {
    echo "✅ 爬虫统计表存在<br>";
    
    // 显示表结构
    $columns = $DB->fetch_all("SHOW COLUMNS FROM `$table`");
    echo "<strong>表结构:</strong><br>";
    foreach ($columns as $col) {
        echo "- {$col['Field']} ({$col['Type']})<br>";
    }
} else {
    echo "❌ 爬虫统计表不存在<br>";
}

// 测试2: 测试爬虫识别
echo "<h3>2. 测试爬虫识别</h3>";
$test_agents = array(
    'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)',
    'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)',
    'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
);

foreach ($test_agents as $agent) {
    $spider_type = identify_spider($agent);
    $short_agent = substr($agent, 0, 50) . '...';
    if ($spider_type) {
        echo "✅ {$short_agent} -> {$spider_type}<br>";
    } else {
        echo "❌ {$short_agent} -> 非爬虫<br>";
    }
}

// 测试3: 测试今日统计获取
echo "<h3>3. 测试今日统计获取</h3>";
try {
    $today_stats = get_today_spider_stats();
    echo "✅ 成功获取今日统计数据<br>";
    echo "<strong>今日统计:</strong><br>";
    echo "- Google: {$today_stats['google_count']}<br>";
    echo "- 百度: {$today_stats['baidu_count']}<br>";
    echo "- 必应: {$today_stats['bing_count']}<br>";
    echo "- 总访问: {$today_stats['total_visits']}<br>";
    echo "- 总站点: {$today_stats['total_sites']}<br>";
    echo "- 总文章: {$today_stats['total_articles']}<br>";
    echo "- 出站链接: {$today_stats['total_outlinks']}<br>";
} catch (Exception $e) {
    echo "❌ 获取今日统计失败: " . $e->getMessage() . "<br>";
}

// 测试4: 模拟爬虫访问
echo "<h3>4. 模拟爬虫访问测试</h3>";
try {
    // 获取访问前的统计
    $before_stats = get_today_spider_stats();
    echo "<strong>访问前统计:</strong><br>";
    echo "- Google: {$before_stats['google_count']}<br>";
    echo "- 百度: {$before_stats['baidu_count']}<br>";
    echo "- 必应: {$before_stats['bing_count']}<br><br>";

    // 模拟多次Google爬虫访问
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (compatible; Googlebot/2.1; +http://www.google.com/bot.html)';
    for ($i = 0; $i < 3; $i++) {
        track_visitor();
    }
    echo "✅ 模拟3次Google爬虫访问成功<br>";

    // 模拟多次百度爬虫访问
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)';
    for ($i = 0; $i < 2; $i++) {
        track_visitor();
    }
    echo "✅ 模拟2次百度爬虫访问成功<br>";

    // 模拟必应爬虫访问
    $_SERVER['HTTP_USER_AGENT'] = 'Mozilla/5.0 (compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm)';
    track_visitor();
    echo "✅ 模拟1次必应爬虫访问成功<br>";

    // 重新获取统计数据
    $updated_stats = get_today_spider_stats();
    echo "<strong>更新后的统计:</strong><br>";
    echo "- Google: {$updated_stats['google_count']} (增加了" . ($updated_stats['google_count'] - $before_stats['google_count']) . ")<br>";
    echo "- 百度: {$updated_stats['baidu_count']} (增加了" . ($updated_stats['baidu_count'] - $before_stats['baidu_count']) . ")<br>";
    echo "- 必应: {$updated_stats['bing_count']} (增加了" . ($updated_stats['bing_count'] - $before_stats['bing_count']) . ")<br>";

} catch (Exception $e) {
    echo "❌ 模拟爬虫访问失败: " . $e->getMessage() . "<br>";
}

// 测试5: 测试AJAX接口
echo "<h3>5. 测试AJAX接口</h3>";
try {
    $_GET['ajax'] = '1';
    ob_start();
    include('module/datastats.php');
    $ajax_output = ob_get_clean();
    
    $json_data = json_decode($ajax_output, true);
    if ($json_data) {
        echo "✅ AJAX接口正常<br>";
        echo "<strong>返回数据:</strong><br>";
        if (isset($json_data['spider_stats'])) {
            echo "- 爬虫统计: " . json_encode($json_data['spider_stats']) . "<br>";
        }
        if (isset($json_data['today_stats'])) {
            echo "- 今日统计: " . json_encode($json_data['today_stats']) . "<br>";
        }
    } else {
        echo "❌ AJAX接口返回数据格式错误<br>";
        echo "原始输出: " . htmlspecialchars($ajax_output) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ AJAX接口测试失败: " . $e->getMessage() . "<br>";
}

echo "<h3>测试完成</h3>";
echo "<p><a href='?mod=datastats'>查看数据公示页面</a></p>";
echo "<p><a href='index.php'>返回首页</a></p>";
?>
