<?php
/* Smarty version 4.5.5, created on 2025-07-14 05:46:41
  from '/www/wwwroot/www.95dir.com/themes/system/blacklist_detail.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.5.5',
  'unifunc' => 'content_68742941a21db3_02527998',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    'efe31ddee647700fe1f3fc5b4cf6faeacf014104' => 
    array (
      0 => '/www/wwwroot/www.95dir.com/themes/system/blacklist_detail.html',
      1 => 1752238938,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
    'file:header.html' => 1,
    'file:footer.html' => 1,
  ),
),false)) {
function content_68742941a21db3_02527998 (Smarty_Internal_Template $_smarty_tpl) {
$_smarty_tpl->_subTemplateRender("file:header.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
?>

<h3 class="title">
    <em>黑名单详情</em>
    <span>
        <a href="blacklist.php">返回列表&raquo;</a>
    </span>
</h3>

<div class="formbox">
    <table width="100%" border="0" cellspacing="1" cellpadding="0">
        <tr>
            <th width="120">网站ID：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
</td>
        </tr>
        <tr>
            <th>网站名称：</th>
            <td><strong><?php echo $_smarty_tpl->tpl_vars['website']->value['web_name'];?>
</strong></td>
        </tr>
        <tr>
            <th>网站地址：</th>
            <td>
                <a href="http://<?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>
" target="_blank" style="color: #007cba;">
                    <?php echo $_smarty_tpl->tpl_vars['website']->value['web_url'];?>

                </a>
            </td>
        </tr>
        <tr>
            <th>所属分类：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['cate_name'];?>
</td>
        </tr>
        <tr>
            <th>黑名单分类：</th>
            <td>
                <span style="color: #f00; font-weight: bold;">
                    <?php echo $_smarty_tpl->tpl_vars['website']->value['category_name'];?>

                </span>
            </td>
        </tr>
        <tr>
            <th>拉黑理由：</th>
            <td>
                <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; line-height: 1.6;">
                    <?php if ($_smarty_tpl->tpl_vars['website']->value['web_blacklist_reason']) {?>
                        <?php echo nl2br((string) $_smarty_tpl->tpl_vars['website']->value['web_blacklist_reason'], (bool) 1);?>

                    <?php } else { ?>
                        <span style="color: #999;">未填写拉黑理由</span>
                    <?php }?>
                </div>
            </td>
        </tr>
        <tr>
            <th>网站简介：</th>
            <td>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 3px; line-height: 1.5;">
                    <?php echo nl2br((string) $_smarty_tpl->tpl_vars['website']->value['web_intro'], (bool) 1);?>

                </div>
            </td>
        </tr>
        <tr>
            <th>网站标签：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['web_tags'];?>
</td>
        </tr>
        <tr>
            <th>提交时间：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['ctime_formatted'];?>
</td>
        </tr>
        <tr>
            <th>拉黑时间：</th>
            <td>
                <span style="color: #f00;">
                    <?php echo $_smarty_tpl->tpl_vars['website']->value['blacklist_time_formatted'];?>

                </span>
            </td>
        </tr>
        <tr>
            <th>操作员：</th>
            <td><?php echo $_smarty_tpl->tpl_vars['website']->value['web_blacklist_operator'];?>
</td>
        </tr>
        <tr class="btnbox">
            <th>&nbsp;</th>
            <td>
                <a href="website.php?act=edit&web_id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" class="btn">编辑网站</a>
                &nbsp;
                <a href="blacklist.php?act=restore&id=<?php echo $_smarty_tpl->tpl_vars['website']->value['web_id'];?>
" 
                   class="btn btn-success" 
                   onclick="return confirm('确认恢复此网站为待审核状态吗？');">
                    恢复网站
                </a>
                &nbsp;
                <a href="blacklist.php" class="btn btn-default">返回列表</a>
            </td>
        </tr>
    </table>
</div>

<style>
.formbox table th {
    text-align: right;
    vertical-align: top;
    padding: 10px;
    background: #f8f9fa;
    font-weight: normal;
}

.formbox table td {
    padding: 10px;
    vertical-align: top;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.btn {
    background: #007cba;
    color: white;
}

.btn:hover {
    background: #005a87;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #218838;
}

.btn-default {
    background: #6c757d;
    color: white;
}

.btn-default:hover {
    background: #5a6268;
}

.title span a {
    color: #007cba;
    text-decoration: none;
}

.title span a:hover {
    text-decoration: underline;
}
</style>

<?php $_smarty_tpl->_subTemplateRender("file:footer.html", $_smarty_tpl->cache_id, $_smarty_tpl->compile_id, 0, $_smarty_tpl->cache_lifetime, array(), 0, false);
}
}
