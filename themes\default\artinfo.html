<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />

{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="artinfo">
            	<h1 class="atitle">{#$art.art_title#}</h1>
				<div class="aattr">来源：<a href="{#$art.copy_url#}" target="_blank">{#$art.copy_from#}</a>　浏览：{#$art.art_views#}次　时间：{#$art.art_ctime#}</div>
				<div class="aattr1"><b>简介</b>：{#$art.art_intro#}</div>
				<div class="content">{#$art.art_content#}</div>
				<div class="hcatelist">
				    <li><i class="fas fa-tag"></i>标签：
				    {#foreach from=$art_tags item=tag name=tags#}
				        <a href="{#$tag.tag_link#}" title="查看更多关于'{#$tag.tag_name#}'的文章">{#$tag.tag_name#}</a>{#if !$smarty.foreach.tags.last#}{#/if#}
				    {#foreachelse#}
				        暂无标签
				    {#/foreach#}
				    </li>
				    </div>
				    
				    <div class="text-xs text-muted"><div><span>©</span> 版权声明</div><div class="posts-copyright"><div><br><fieldset style="border:1px dashed #008CFF;padding:10px;border-radius:8px;line-height: 2em;color: #6D6D6D"><legend align="center" style="color:#FFFFFF;width:200px;text-align:center;background-color:#008CFF;font-size: 14px;border-radius: 5px">95分类目录 - 版权声明</legend>1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br> 2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br> 3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br> 4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br> 5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br> 6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br> 7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。</fieldset><br></div></div></div>
				    <!-- 打赏按钮 -->
				    <div class="donate-button-container">
<button onclick="showDonatePopup()">打赏支持</button>
<div class="fenxiang"><div class="social-share"></div></div>
</div><!-- 打赏弹窗 -->
<div id="donate-popup" style="display:none;">
<div class="donate-popup-content">
<span class="close" onclick="closeDonatePopup()">&times;</span>
<h3>您的赞助更新的动力</h3>
<div class="donate-qr-codes">
<div>
<h4>微信打赏</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="WeChat QR Code">
</div>
<div>
<h4>支付宝打赏</h4>
<img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="Alipay QR Code">
</div>
</div>
</div>
</div>
                <ul class="prevnext">
                	<li>上一篇： {#if !empty($prev)#}<a href="{#$prev.art_link#}">{#$prev.art_title#}</a>{#else#}没有了{#/if#}</li>
                    <li>下一篇： {#if !empty($next)#}<a href="{#$next.art_link#}">{#$next.art_title#}</a>{#else#}没有了{#/if#}</li>
                </ul>
            </div>
            <div class="blank10"></div>
        </div>
        <div id="mainbox-right">
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>
</body>
</html>